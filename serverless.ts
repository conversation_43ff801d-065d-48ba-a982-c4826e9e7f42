import type { AWS } from "@serverless/typescript";
import { limSfQueue } from "@functions/lim-service";
import { oapStudentNotification } from "@functions/notification-service";
import { sqsLambdaTrigger } from "@functions/sqs-lambda";
import { ibatSfQueue } from "@functions/ibat-el-service";
import { oapLogsExporter } from "@functions/oap-logs-exporter-service";
import { hzuSfQueue, hzuChangeRequestQueue } from "@functions/hzu-service";
import { ucwSfQueue } from "@functions/ucw-service";
import {
  cognitoCustomEmailTrigger,
  cognitoPreSignUpTrigger,
  cognitoPreAuthenticationTrigger,
  cognitoPostConfirmationTrigger,
} from "@functions/cognito-trigger";
import { customAuthorizer } from "@functions/custom-auth-service";
import { unfcSfQueue, unfcChangeRequestQueue } from "@functions/unfc-service";
import { studentLeadOwnerAssignment } from "@functions/student-service";
import {
  ucw<PERSON>eg<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ucwLegacyReviewedAppHandler,
} from "@functions/ucw-legacy-apps";
import { lsbfmyrSfQueue } from "@functions/lsbfmyr-service";
import { s3MetadataModifier } from "@functions/s3MetadataModifier";
import { uegSfQueue } from "@functions/ueg-service";
import { wulSfQueue } from "@functions/wul-service";
import { ardSfQueue } from "@functions/ard-service";
import { puSfQueue } from "@functions/pu-service";

const serverlessConfiguration: AWS = {
  service: "oap-handler-service",
  frameworkVersion: "3",
  plugins: ["serverless-esbuild", "serverless-offline"],
  provider: {
    name: "aws",
    runtime: "nodejs16.x",
    region: "eu-west-1",
    stage: "${opt:stage}",
    role: "arn:aws:iam::${aws:accountId}:role/oap-lambda-exec-role-${self:provider.stage}",
    environment: {
      STAGE: "${self:provider.stage}",
      AWS_NODEJS_CONNECTION_REUSE_ENABLED: "1",
      LIM_OAP_LOGS_EXPORTS_BUCKET_NAME:
        "${self:custom.variables.limOaplogsExportsBucketName.${self:provider.stage}}",
      OAP_LOGS_EXPORTS_BUCKET_NAME:
        "${self:custom.variables.logsExportsBucketName.${self:provider.stage}}",
      NODE_OPTIONS:
        "${self:custom.variables.nodeOptions.${self:provider.stage}}",
      GUS_MIDDLEWARE_API_KEY:
        "${self:custom.variables.gusMiddlewareAPIKey.${self:provider.stage}}",
      GUS_MIDDLEWARE_API:
        "${self:custom.variables.gusMiddlewareBaseUrl.${self:provider.stage}}",
      OAP_API: "${self:custom.variables.oapApi.${self:provider.stage}}",
      LOGGER_VERSION:
        "${self:custom.variables.loggerVersion.${self:provider.stage}}",
      LOGGER_LOG_GROUP_NAME:
        "${self:custom.variables.logGroupName.${self:provider.stage}}",
      REGION: "${self:custom.variables.region.${self:provider.stage}}",
      OAP_NOTIFICATION_TOPIC_ARN:
        "${self:custom.variables.oapTopicARN.${self:provider.stage}}",
      OAP_NOTIFICATION_TOPIC_NAME:
        "${self:custom.variables.stagePrefix.${self:provider.stage}}-GUS-OAP-STUDENT-NOTIFICATION-TOPIC",
      SES_SENDER_EMAIL: "<EMAIL>",
      UCW_SES_SENDER_EMAIL: "<EMAIL>",
      LSBFMYR_SES_SENDER_EMAIL: "<EMAIL>",
      UEG_SES_SENDER_EMAIL: "<EMAIL>",
      UCW_KEY: "rLknjx19jUaPaoqGtbLawha8ylaHwBs8zP6aoGrh",
      HZU_KEY: "yphESfRh2o5J7L87WsKfh2MIBWSdPHev5TNPSGNZ",
      PU_KEY:"tUvJaSIY8e2HqB24JlOcx6RtUDGfHVyi6QWSrTns",
      HZU_EIP_QUEUE:
        "${self:custom.variables.hzuEIPQueue.${self:provider.stage}}",
      LIM_EIP_QUEUE:
        "${self:custom.variables.limEIPQueue.${self:provider.stage}}",
      LIM_EIP_OUEUE_URL:
        "${self:custom.variables.limEIPQueueUrl.${self:provider.stage}}",
      IBAT_EIP_QUEUE:
        "${self:custom.variables.ibatEIPQueue.${self:provider.stage}}",
      UCW_AGENT_EIP_QUEUE:
        "${self:custom.variables.ucwEIPQueue.${self:provider.stage}}",
      UEG_AGENT_EIP_QUEUE:
        "${self:custom.variables.uegEIPQueue.${self:provider.stage}}",
      UNFC_AGENT_EIP_QUEUE:
        "${self:custom.variables.unfcEIPQueue.${self:provider.stage}}",
      LSBFMYR_EIP_QUEUE:
        "${self:custom.variables.lsbfmyrEIPQueue.${self:provider.stage}}",
      S3_BUCKET_ACCESS_ROLE_ARN:
        "${self:custom.variables.s3roleArn.${self:provider.stage}}",
      REVIEW_CENTER_BUCKET_NAME:
        "${self:custom.variables.reviewCenterBucket.${self:provider.stage}}",
      CAMPUSNET_BUCKET_NAME:
        "${self:custom.variables.campusnetBucket.${self:provider.stage}}",
      APPHERO_AUTH_PROVIDER_API:
        "${self:custom.variables.appheroAuthProviderApi.${self:provider.stage}}",
      APPHERO_AUTH_PROVIDER_CLIENT_ID:
        "${self:custom.variables.appheroAuthProviderClientId.${self:provider.stage}}",
      APPHERO_AUTH_PROVIDER_CLIENT_SECRET:
        "${self:custom.variables.appheroAuthProviderClientSecret.${self:provider.stage}}",
      HZU_COGNITO: "${self:custom.variables.hzuCognito.${self:provider.stage}}",
      LIM_COGNITO: "${self:custom.variables.limCognito.${self:provider.stage}}",
      UCW_COGNITO: "${self:custom.variables.ucwCognito.${self:provider.stage}}",
      UNFC_COGNITO:
        "${self:custom.variables.unfcCognito.${self:provider.stage}}",
      UEG_COGNITO: "${self:custom.variables.uegCognito.${self:provider.stage}}",
      TEAMS_WEBHOOK_URL:
        "${self:custom.variables.teamsWebhookUrl.${self:provider.stage}}",
      IS_ALERT_NEEDED:
        "${self:custom.variables.isAlertNeeded.${self:provider.stage}}",
      API_KEYS: "${self:custom.variables.apiKeys}",
      OAP_SF_TOPIC_ARN:
        "${self:custom.variables.oapTopic.${self:provider.stage}}",
      STUDENT_OAP_LEAD_ASSIGNMENT_QUEUE:
        "${self:custom.variables.studentOapLeadAssignmentQueue.${self:provider.stage}}",
      UCW_LEGACY_APPLICATION_QUEUE_ARN:
        "${self:custom.variables.ucwLegacyApplicationQueueArn.${self:provider.stage}}",
      // Mutual authentication configuration
      MUTUAL_AUTH_ENABLED:
        "${self:custom.variables.mutualAuthEnabled.${self:provider.stage}}",
      DEV_APPHERO_COGNITO:
        "${self:custom.variables.devAppheroCognito.${self:provider.stage}}",
      WUL_AGENT_EIP_QUEUE:
        "${self:custom.variables.wulAgentEipQueue.${self:provider.stage}}",
      ARD_AGENT_EIP_QUEUE:
        "${self:custom.variables.ardAgentEipQueue.${self:provider.stage}}",
      PU_AGENT_EIP_QUEUE:
        "${self:custom.variables.puAgentEipQueue.${self:provider.stage}}",
      APPHERO_SERVER_DOMAIN:
        "${self:custom.variables.appHeroServerDomain.${self:provider.stage}}",
      APPHERO_DOMAIN:
        "${self:custom.variables.appheroDomain.${self:provider.stage}}",
    },
  },
  // import the function via paths
  functions: {
    oapLogsExporter,
    sqsLambdaTrigger,
    ibatSfQueue,
    limSfQueue,
    oapStudentNotification,
    hzuSfQueue,
    ucwSfQueue,
    uegSfQueue,
    cognitoCustomEmailTrigger,
    cognitoPreSignUpTrigger,
    cognitoPreAuthenticationTrigger,
    cognitoPostConfirmationTrigger,
    customAuthorizer,
    unfcSfQueue,
    studentLeadOwnerAssignment,
    ucwLegacyAppHandler,
    ucwLegacyReviewedAppHandler,
    lsbfmyrSfQueue,
    s3MetadataModifier,
    wulSfQueue,
    ardSfQueue,
    puSfQueue,
    hzuChangeRequestQueue,
    unfcChangeRequestQueue,
  },
  package: { individually: true },
  custom: {
    esbuild: {
      bundle: true,
      minify: false,
      sourcemap: true,
      exclude: ["aws-sdk"],
      target: "node16",
      define: { "require.resolve": undefined },
      platform: "node",
      concurrency: 10,
    },
    variables: {
      nodeOptions: {
        dev: "--enable-source-maps --stack-trace-limit=1000",
        prod: "--enable-source-maps --stack-trace-limit=1000",
      },
      limOaplogsExportsBucketName: {
        dev: "lim-csv-log-exports-dev",
        prod: "lim-csv-log-exports-prod",
      },
      logsExportsBucketName: {
        dev: "oap-logs-exports-dev",
        prod: "oap-logs-exports-prod",
      },
      gusMiddlewareAPIKey: {
        dev: "yphESfRh2o5J7L87WsKfh2MIBWSdPDev4TNPSGNZ",
        prod: "yphESfRh2o5J7L87WsKfh2MIBWSdPDev4TNPSGNZ",
      },
      gusMiddlewareBaseUrl: {
        dev: "https://dev-api.guseip.io",
        prod: "https://api.guseip.io",
      },
      oapApi: {
        dev: "https://oap-dev-api.apphero.io",
        prod: "https://oap-api.apphero.io",
      },
      loggerVersion: {
        dev: "v1",
        prod: "v1",
      },
      logGroupName: {
        dev: "oap-loggers-dev",
        prod: "oap-loggers-prod",
      },
      stagePrefix: {
        dev: "DEV",
        prod: "PROD",
      },
      region: {
        dev: "eu-west-1",
        prod: "eu-west-1",
      },
      oapTopicARN: {
        dev: "arn:aws:sns:eu-west-1:${aws:accountId}:DEV-GUS-OAP-STUDENT-NOTIFICATION-TOPIC",
        prod: "arn:aws:sns:eu-west-1:${aws:accountId}:PROD-GUS-OAP-STUDENT-NOTIFICATION-TOPIC",
      },
      hzuEIPQueue: {
        dev: "arn:aws:sqs:eu-west-1:${aws:accountId}:DEV-HZU-EIP-QUEUE.fifo",
        prod: "arn:aws:sqs:eu-west-1:${aws:accountId}:PROD-HZU-EIP-QUEUE.fifo",
      },
      studentOapLeadAssignmentQueue: {
        dev: "arn:aws:sqs:eu-west-1:${aws:accountId}:DEV-STUDENT-OAP-LEAD-OWNER-CHANGE-QUEUE.fifo",
        prod: "arn:aws:sqs:eu-west-1:${aws:accountId}:PROD-STUDENT-OAP-LEAD-OWNER-CHANGE-QUEUE.fifo",
      },
      limEIPQueue: {
        dev: "arn:aws:sqs:eu-west-1:${aws:accountId}:DEV-LIM-EIP-QUEUE.fifo",
        prod: "arn:aws:sqs:eu-west-1:${aws:accountId}:PROD-LIM-EIP-QUEUE.fifo",
      },
      limEIPQueueUrl: {
        dev: "https://sqs.eu-west-1.amazonaws.com/${aws:accountId}/DEV-LIM-EIP-QUEUE.fifo",
        prod: "https://sqs.eu-west-1.amazonaws.com/${aws:accountId}/PROD-LIM-EIP-QUEUE.fifo",
      },
      ibatEIPQueue: {
        dev: "arn:aws:sqs:eu-west-1:${aws:accountId}:DEV-IBAT-EIP-QUEUE.fifo",
        prod: "arn:aws:sqs:eu-west-1:${aws:accountId}:PROD-IBAT-EIP-QUEUE.fifo",
      },
      ucwEIPQueue: {
        dev: "arn:aws:sqs:eu-west-1:${aws:accountId}:DEV-UCW-AGENT-EIP-QUEUE.fifo",
        prod: "arn:aws:sqs:eu-west-1:${aws:accountId}:PROD-UCW-AGENT-EIP-QUEUE.fifo",
      },
      uegEIPQueue: {
        dev: "arn:aws:sqs:eu-west-1:${aws:accountId}:DEV-UEG-EIP-QUEUE.fifo",
        prod: "arn:aws:sqs:eu-west-1:${aws:accountId}:PROD-UEG-EIP-QUEUE.fifo",
      },
      unfcEIPQueue: {
        dev: "arn:aws:sqs:eu-west-1:${aws:accountId}:DEV-UNFC-EIP-QUEUE.fifo",
        prod: "arn:aws:sqs:eu-west-1:${aws:accountId}:PROD-UNFC-EIP-QUEUE.fifo",
      },
      lsbfmyrEIPQueue: {
        dev: "arn:aws:sqs:eu-west-1:${aws:accountId}:DEV-LSBFMYR-EIP-QUEUE.fifo",
        prod: "arn:aws:sqs:eu-west-1:${aws:accountId}:PROD-LSBFMYR-EIP-QUEUE.fifo",
      },
      wulAgentEipQueue: {
        dev: "arn:aws:sqs:eu-west-1:${aws:accountId}:DEV-WUL-EIP-QUEUE.fifo",
        prod: "arn:aws:sqs:eu-west-1:${aws:accountId}:PROD-WUL-EIP-QUEUE.fifo",
      },
      ardAgentEipQueue: {
        dev: "arn:aws:sqs:eu-west-1:${aws:accountId}:DEV-ARD-EIP-QUEUE.fifo",
        prod: "arn:aws:sqs:eu-west-1:${aws:accountId}:PROD-ARD-EIP-QUEUE.fifo",
      },
      puAgentEipQueue:{
        dev:"arn:aws:sqs:eu-west-1:${aws:accountId}:DEV-PU-EIP-QUEUE.fifo",
        prod:""
      },
      ucwLegacyApplicationQueueArn: {
        dev: "arn:aws:sqs:eu-west-1:${aws:accountId}:DEV-UCW-LEGACY-APPLICATION-QUEUE.fifo",
        prod: "arn:aws:sqs:eu-west-1:${aws:accountId}:PROD-UCW-LEGACY-APPLICATION-QUEUE.fifo",
      },
      s3roleArn: {
        dev: "arn:aws:iam::************:role/s3OAPCrossAccountAccessRole-prod",
        prod: "arn:aws:iam::************:role/s3OAPCrossAccountAccessRole-prod",
      },
      reviewCenterBucket: {
        dev: "reviewcenter-stage",
        prod: "reviewcenter",
      },
      campusnetBucket: {
        dev: "campusnet-sf-sync-stage",
        prod: "campusnet-sf-sync",
      },
      appheroAuthProviderApi: {
        dev: "https://dev-authprovider.apphero.io/oauth2/token",
        prod: "https://authprovider.apphero.io/oauth2/token",
      },
      appheroAuthProviderClientId: {
        dev: "7mqos7b4d7tlmldnur9ifd7u85",
        prod: "vhqiro68jcbthkamjear9ubr6",
      },
      appheroAuthProviderClientSecret: {
        dev: "1j96kmbcb5m3mhttsim5pu03ann3cufeqn386qqe6mqjiqkj02ds",
        prod: "1ih593dqfctcr1vc8278j6rh10e8e08ovlnd7kisi9udh40aopbd",
      },
      hzuCognito: {
        dev: '{"userPoolId":"eu-west-1_4zuOP58FI","clientId":"4j950h9dl8skm5gfg36thh5u6c"}',
        prod: '{"userPoolId":"eu-west-1_4zuOP58FI","clientId":"4j950h9dl8skm5gfg36thh5u6c"}',
      },
      limCognito: {
        dev: '{"userPoolId":"eu-west-1_w5fuKTnEJ","clientId":"2qjqh02ucokk8pvg9g9de5c75n"}',
        prod: '{"userPoolId":"eu-west-1_w5fuKTnEJ","clientId":"2qjqh02ucokk8pvg9g9de5c75n"}',
      },
      ucwCognito: {
        dev: '{"userPoolId":"eu-west-1_isJtz7XfY","clientId":"mi02thfgb5ql794ejc72c0ae8"}',
        prod: '{"userPoolId":"eu-west-1_3PvBCuLRl","clientId":"5mt1kt84vs1ag22ksndta6g0lg"}',
      },
      unfcCognito: {
        dev: '{"userPoolId":"eu-west-1_4yORyxiwf","clientId":"3qv53jimv05is5nl8rd9c8oh"}',
        prod: '{"userPoolId":"eu-west-1_4yORyxiwf","clientId":"3qv53jimv05is5nl8rd9c8oh"}',
      },
      uegCognito: {
        dev: '{"userPoolId":"eu-west-1_SRMEFiZsz","clientId":"2dpk0k208esuf571siuibr1eop"}',
        prod: '{"userPoolId":"eu-west-1_SRMEFiZsz","clientId":"2dpk0k208esuf571siuibr1eop"}',
      },
      teamsWebhookUrl: {
        dev: "https://gus.webhook.office.com/webhookb2/915b40cd-36b9-407e-acfe-0c8902cda66a@5665ee7a-3634-4975-9c21-2778cda48edd/IncomingWebhook/6b1a4014ad054be892840c3b0f3154c9/d3348445-411e-47c1-bce3-f0508548f373/V2DWSnqkBZcr_iRiRZb1b3r08QQO3rE9VCfQacX9Tknn01",
        prod: "https://gus.webhook.office.com/webhookb2/915b40cd-36b9-407e-acfe-0c8902cda66a@5665ee7a-3634-4975-9c21-2778cda48edd/IncomingWebhook/9347fcaa509e4a94971a9bc3dfc36219/d3348445-411e-47c1-bce3-f0508548f373/V2sw1Kw5EvVxsdTLJYa1Ivchr_KVDxeBwy7AAFJ5HZEqM1",
      },
      isAlertNeeded: {
        dev: true,
        prod: true,
      },
      oapTopic: {
        dev: "arn:aws:sns:eu-west-1:933713876074:DEV-GUS-OAP-SF-TOPIC.fifo",
        prod: "arn:aws:sns:eu-west-1:************:PROD-GUS-OAP-SF-TOPIC.fifo",
      },
      apiKeys:
        '{"UCW":"rLknjx19jUaPaoqGtbLawha8ylaHwBs8zP6aoGrh","LIM":"yphESfRh2o5J7L87WsKfh2MIBWSdPHev4TNPSGNZ","HZU":"yphESfRh2o5J7L87WsKfh2MIBWSdPHev5TNPSGNZ","IBAT":"yphESfRh2o5J7L87WsKfh2MIBWSdPDev4TNPSGNZ","UNFC":"yphESfRh2o5J7L87WsKfh2MIBWSdPHev6TNPSGNZ","UEG":"zxTQGpLm9r2K5V34YpMfX8NOCJWdBHtv7RLDSAEK"}',
      // Mutual authentication configuration
      mutualAuthEnabled: {
        dev: "true",
        prod: "false",
      },
      devAppheroCognito: {
        dev: '{"userPoolId":"eu-west-1_W2pHL0wmd","clientId":"5t0r9uvee31oqnba1j4rr0uuo2"}',
        prod: '{"userPoolId":"eu-west-1_Q5t0ZyN4K","clientId":"7a9oe1juvo0c326b5j5e2c31gd"}',
      },
      appHeroServerDomain: {
        dev: "https://dev-api.apphero.io",
        prod: "https://api.apphero.io",
      },
      appheroDomain: {
        dev: "https://stage.apphero.io",
        prod: "https://www.apphero.io",
      },
    },
  },
};

module.exports = serverlessConfiguration;


