import { postData } from "src/connectors/salesforce-connectors";
import { getData, postOAPData } from "src/connectors/oap-connectors";
import * as AWS from "aws-sdk";
import { v4 as uuidv4 } from "uuid";
import { CloudWatchLoggerService, LoggerEnum } from "@gus-eip/loggers";
import {
  salesforceAgentConfig,
  salesforceAgentSubObjectsConfig,
  salesforceStudentConfig,
  salesforceStudentSubObjectsConfig,
} from "src/functions/unfc-service/salesforceconfig";
const dynamoDB = new AWS.DynamoDB.DocumentClient({
  region: process.env.REGION,
});

// Initialize logger
const cloudWatchLoggerService = new CloudWatchLoggerService(
  process.env.REGION,
  process.env.LOGGER_LOG_GROUP_NAME,
  process.env.TEAMS_WEBHOOK_URL,
  true
);
const loggerEnum = new LoggerEnum();

let correlationId: string;
let oppId: string;
let email: string;
let applicationId: string;
let existingApplicationId: string;
let scenario: string;
let brand = "UNFC";
let mode: string;
let mainConfig: any;
let subObjectConfig: any;

/**
 * Get API key for UNFC from the API_KEYS environment variable
 */
function getUnfcApiKey(): string {
  try {
    const apiKeys = JSON.parse(process.env.API_KEYS || '{}');
    return apiKeys.UNFC || '';
  } catch (error) {
    console.error('Error parsing API_KEYS:', error);
    return '';
  }
}

let studentDetails: any;
let allSections: any;
let sfResponse: any;
let documents = [];

// Validation interfaces
interface PlatformEventMessage {
  payload: {
    Opportunity_Id__c: string;
    Scenario__c?: string;
    Product_Id__c?: string;
  };
  event?: {
    EventUuid: string;
  };
}

interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

// Validation functions

function validatePlatformEventMessage(message: any): ValidationResult {
  const errors: string[] = [];

  if (!message) {
    errors.push("Platform event message is required");
    return { isValid: false, errors };
  }

  if (!message.payload) {
    errors.push("Platform event payload is required");
  } else {
    if (!message.payload.Opportunity_Id__c) {
      errors.push("Opportunity_Id__c is required in payload");
    }
  }

  if (!message.event?.EventUuid) {
    errors.push("EventUuid is required in event");
  }

  return { isValid: errors.length === 0, errors };
}

function validateRequiredData(data: any, fieldName: string): ValidationResult {
  const errors: string[] = [];

  if (!data) {
    errors.push(`${fieldName} is required but not found`);
  }

  return { isValid: errors.length === 0, errors };
}

// Logging utility functions
async function logInfo(
  event: string,
  message: string,
  payload?: any,
  destinationPayload?: any,
  destinationResponse?: any
): Promise<void> {
  try {
    await cloudWatchLoggerService.log(
      correlationId || `unfc-deferral-${Date.now()}`,
      new Date().toISOString(),
      loggerEnum.Component.OAP_HANDLERS,
      loggerEnum.Component.GUS_SALESFORCE_EVENTS_LISTENER,
      loggerEnum.Component.OAP_BACKEND,
      event,
      loggerEnum.UseCase.DEFERRAL_PROCESSING,
      payload || {},
      destinationPayload || {},
      message,
      brand,
      email || "unknown",
      `oap-handlers/${oppId}/${correlationId}`,
      "Opportunity_Id__c",
      oppId || "unknown",
      "",
      "",
      "",
      "",
      destinationResponse
    );
  } catch (logError) {
    console.error("Failed to log info:", logError);
  }
}

async function logError(
  event: string,
  message: string,
  payload?: any,
  destinationPayload?: any
): Promise<void> {
  try {
    await cloudWatchLoggerService.error(
      correlationId || `unfc-deferral-${Date.now()}`,
      new Date().toISOString(),
      loggerEnum.Component.OAP_HANDLERS,
      loggerEnum.Component.GUS_SALESFORCE_EVENTS_LISTENER,
      loggerEnum.Component.OAP_BACKEND,
      event,
      loggerEnum.UseCase.DEFERRAL_PROCESSING,
      payload || {},
      destinationPayload || {},
      typeof message === "string" ? message : JSON.stringify(message),
      brand,
      email || "unknown",
      `oap-handlers/${oppId}/${correlationId}`,
      "Opportunity_Id__c",
      oppId || "unknown"
    );
  } catch (logErr) {
    console.error("Failed to log error:", logErr);
  }
}

// Retry utility function
async function retryApiCall<T>(
  apiCall: () => Promise<T>,
  operation: string,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> {
  let lastError: any;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const result = await apiCall();
      if (attempt > 1) {
        await logInfo(
          `${operation}_RETRY_SUCCESS`,
          `API call succeeded on attempt ${attempt}`
        );
      }
      return result;
    } catch (error) {
      lastError = error;

      if (attempt === maxRetries) {
        await logError(
          `${operation}_RETRY_FAILED`,
          `API call failed after ${maxRetries} attempts`,
          { error, operation, attempts: maxRetries }
        );
        break;
      }

      const delay = baseDelay * Math.pow(2, attempt - 1); // Exponential backoff
      await logError(
        `${operation}_RETRY_ATTEMPT`,
        `API call failed on attempt ${attempt}, retrying in ${delay}ms`,
        { error, operation, attempt, delay }
      );

      await new Promise((resolve) => setTimeout(resolve, delay));
    }
  }

  throw lastError;
}
export const handleUnfcDeferral = async (record: any) => {
  const startTime = Date.now();

  try {
    // Validate single record structure
    if (!record) {
      await logError(
        loggerEnum.Event.INVALID_EVENT_STRUCTURE,
        "Record is required but not provided",
        { record }
      );
      throw new Error("Record is required but not provided");
    }

    await logInfo(
      loggerEnum.Event.DEFERRAL_PROCESSING_INITIATED,
      "UNFC deferral processing started for single record",
      {
        messageId: record.messageId,
      }
    );

    let platformEventMessage: PlatformEventMessage;

    try {
      platformEventMessage = JSON.parse(record.body);
    } catch (parseError) {
      await logError(
        loggerEnum.Event.MESSAGE_PARSE_ERROR,
        "Failed to parse platform event message",
        { record, error: parseError }
      );
      throw parseError; // Throw error for single record processing
    }

    // Validate platform event message
    const messageValidation =
      validatePlatformEventMessage(platformEventMessage);
    if (!messageValidation.isValid) {
      await logError(
        loggerEnum.Event.INVALID_PLATFORM_EVENT,
        `Platform event validation failed: ${messageValidation.errors.join(
          ", "
        )}`,
        { platformEventMessage, errors: messageValidation.errors }
      );
      throw new Error(
        `Platform event validation failed: ${messageValidation.errors.join(
          ", "
        )}`
      );
    }

    // Extract required fields
    oppId = platformEventMessage.payload.Opportunity_Id__c;
    correlationId =
      platformEventMessage.event?.EventUuid || `unfc-deferral-${Date.now()}`;
    scenario = platformEventMessage?.payload?.Scenario__c;

    await logInfo(
      loggerEnum.Event.PROCESSING_RECORD_INITIATED,
      "Processing deferral record",
      platformEventMessage
    );

    // Get Salesforce object details
    sfResponse = await getObjectDetailsByOppId();
    const sfValidation = validateRequiredData(
      sfResponse,
      "Salesforce response"
    );
    if (!sfValidation.isValid) {
      await logError(
        loggerEnum.Event.SF_DATA_VALIDATION_FAILED,
        `Salesforce data validation failed: ${sfValidation.errors.join(", ")}`,
        { sfResponse, errors: sfValidation.errors }
      );
      throw new Error(
        `Salesforce data validation failed: ${sfValidation.errors.join(", ")}`
      );
    }

    // Determine mode and extract basic info
    sfResponse?.["Opportunity"]?.["ApplicationSource__c"] === "Agent Portal"
      ? (mode = "AGENT")
      : (mode = "DIRECT");
    existingApplicationId =
      sfResponse?.["Opportunity"]?.["ApplicationFormId__c"];
    email = sfResponse?.["Opportunity"]?.["AccountEmail__c"];

    if (!email) {
      await logError(
        loggerEnum.Event.MISSING_EMAIL,
        "Email is required but not found in Salesforce response",
        {
          oppId,
          email,
        }
      );
      throw new Error("Email is required but not found in Salesforce response");
    }

    await logInfo(
      loggerEnum.Event.SF_DATA_RETRIEVED,
      "Salesforce data retrieved successfully",
      {
        mode,
        existingApplicationId,
        email,
      },
      {},
      sfResponse
    );

    // Initialize application details
    let finalStudentJson = await getInitialApplicationDetails();

    // Get student details and form sections in parallel
    [studentDetails, allSections] = await Promise.all([
      getExistingApplicationDetails(),
      getOAPSections(),
    ]);
    // Safely filter sections if it's an array
    if (Array.isArray(allSections)) {
      allSections = allSections.filter(
        (section) => section.SK !== "DEFERRAL_FORM_DEFERRAL_INFO"
      );
    } else {
      allSections = [];
      await logError(
        loggerEnum.Event.INVALID_EVENT_STRUCTURE,
        "allSections is not an array",
        { allSections }
      );
    }

    await logInfo(
      loggerEnum.Event.DATA_RETRIEVAL_COMPLETE,
      "Student details and form sections retrieved",
      {
        hasStudentDetails: !!studentDetails,
        sectionsCount: allSections?.length || 0,
      },
      {},
      studentDetails
    );

    // Set up configuration
    const isAgent = mode === "AGENT";
    mainConfig = isAgent ? salesforceAgentConfig : salesforceStudentConfig;
    subObjectConfig = isAgent
      ? salesforceAgentSubObjectsConfig
      : salesforceStudentSubObjectsConfig;
    delete mainConfig.Lead;
    delete mainConfig.Survey_AP__c;

    // Process form sections to get basic fields
    if (allSections && Array.isArray(allSections)) {
      for (let i = 0; i < allSections.length; i++) {
        const section = allSections[i];
        try {
          const sectionResult = await processFormSections(section?.fieldData);
          Object.assign(finalStudentJson, sectionResult);
        } catch (sectionError) {
          await logError(
            loggerEnum.Event.SECTION_PROCESSING_ERROR,
            `Error processing section ${i}`,
            { section, error: sectionError }
          );
          // Continue processing other sections
        }
      }
    }

    // Process custom fields with error handling
    const identityDocument = processIdentityDocument();
    const connections = processConnections();
    const languageProficiency = processLanguageProficiency();
    const addressInformation = processAddressInformation();
    const visaApplication = processVisaApplication();
    const miscDetails = await processMiscDetails();
    let productDetails = {};
    if (platformEventMessage.payload.Product_Id__c) {
      try {
        productDetails = await getProductDetails(
          platformEventMessage.payload.Product_Id__c
        );
      } catch (productError) {
        await logError(
          loggerEnum.Event.GET_PRODUCT_DETAILS_ERROR,
          "Error retrieving product details",
          {
            productId: platformEventMessage.payload.Product_Id__c,
            error: productError,
          }
        );
      }
    }

    // Merge all data
    finalStudentJson = {
      ...finalStudentJson,
      ...identityDocument,
      ...connections,
      ...languageProficiency,
      ...addressInformation,
      ...visaApplication,
      ...miscDetails,
      ...productDetails,
      assignedTo: sfResponse.Opportunity?.Assigned_To__c,
      documentStatus: "Assigned",
      crApp: true,
      applicationStatus: "inProgress",
      documents: documents,
    };

    // Process field mapping and set default values
    finalStudentJson = await processFieldMappingAndDefaults(finalStudentJson);

    // Convert boolean values back to Yes/No
    finalStudentJson = convertBooleanToYesNo(finalStudentJson);

    await logInfo(
      loggerEnum.Event.DATA_PROCESSING_COMPLETE,
      "Data processing completed",
      {
        finalDataKeys: Object.keys(finalStudentJson),
        documentsCount: documents.length,
      },
      {},
      finalStudentJson
    );

    // Save as in-progress
    await postOAPData(
      `oap/savestudentdetails?oapName=${brand}&mode=${mode}`,
      finalStudentJson,
      getUnfcApiKey()
    );

    // Update to submitted status
    finalStudentJson["applicationStatus"] = "submitted";
    finalStudentJson["programDisplayName"] = studentDetails?.programDisplayName;
    await postOAPData(
      `oap/savestudentdetails?oapName=${brand}&mode=${mode}`,
      finalStudentJson,
      getUnfcApiKey()
    );

    await logInfo(
      loggerEnum.Event.RECORD_PROCESSED_SUCCESSFULLY,
      "Record processed successfully",
      {
        oppId,
        applicationId,
        email,
      }
    );

    const processingTime = Date.now() - startTime;
    await logInfo(
      loggerEnum.Event.DEFERRAL_PROCESSING_COMPLETED,
      "UNFC deferral processing completed for single record",
      {
        processingTimeMs: processingTime,
        messageId: record.messageId,
        oppId,
        applicationId,
      }
    );
  } catch (error) {
    const processingTime = Date.now() - startTime;
    await logError(
      loggerEnum.Event.DEFERRAL_PROCESSING_FAILED,
      "UNFC deferral processing failed for single record",
      {
        error,
        processingTimeMs: processingTime,
        messageId: record?.messageId,
        oppId,
      }
    );
    throw error;
  }
};
export const getProductDetails = async (productId: string) => {
  try {
    if (!productId) {
      await logError(
        loggerEnum.Event.MISSING_PRODUCT_ID,
        "Product ID is required but not provided"
      );
      throw new Error("Product ID is required");
    }

    if (
      !sfResponse?.Opportunity?.Programme__c ||
      !sfResponse?.Opportunity?.Location__c
    ) {
      await logError(
        loggerEnum.Event.MISSING_SF_DATA,
        "Programme or Location missing from Salesforce response",
        {
          programme: sfResponse?.Opportunity?.Programme__c,
          location: sfResponse?.Opportunity?.Location__c,
        }
      );
      throw new Error("Programme and Location are required from Salesforce");
    }

    const products = await getData(
      `oap/lookup/intake/${sfResponse.Opportunity.Programme__c}?location=${sfResponse.Opportunity.Location__c}&brand=UNFC`,
      getUnfcApiKey()
    );

    if (!products || !Array.isArray(products)) {
      await logError(
        loggerEnum.Event.INVALID_PRODUCTS_RESPONSE,
        "Invalid products response from API",
        { products }
      );
      throw new Error("Invalid products response");
    }

    const product = products.find((product) => product.productId === productId);

    if (!product) {
      await logError(
        loggerEnum.Event.PRODUCT_NOT_FOUND,
        "Product not found",
        products
      );
      throw new Error(`Product with ID ${productId} not found`);
    }

    product.intakeDisplayName = product.label;
    product.intake = product.value;

    await logInfo(
      loggerEnum.Event.PRODUCT_DETAILS_RETRIEVED,
      "Product details retrieved successfully",
      {
        productId,
        productLabel: product.label,
      }
    );

    return product;
  } catch (error) {
    await logError(
      loggerEnum.Event.GET_PRODUCT_DETAILS_ERROR,
      "Error retrieving product details",
      {
        productId,
        error: error.message || error,
      }
    );
    throw error;
  }
};
export const getInitialApplicationDetails = async () => {
  console.log("fResponse?.Application__c", sfResponse?.Application__c);
  const initialPayload = {
    firstName: sfResponse?.Account?.FirstName || null,
    lastName: sfResponse?.Account?.LastName || null,
    email: email,
    applicationStatus: "inProgress",
    sectionLabel: "Personal Information",
    agentAccountId: sfResponse?.Opportunity?.AgentAccount__c || null,
    agentContactId: sfResponse?.Opportunity?.Agent_Contact__c || null,
    agentContactUserId: sfResponse?.Application__c?.[0]?.OwnerId || null,
    accountManagerUserId: sfResponse?.Opportunity?.BusinessDeveloper__c || null,
  };

  let initialOapResponse;
  try {
    initialOapResponse = await postOAPData(
      `oap/savestudentdetails?oapName=${brand}&mode=${mode}`,
      initialPayload,
      getUnfcApiKey()
    );
    applicationId = initialOapResponse.applicationId;
    return initialOapResponse;
  } catch (error) {
    console.error("Error saving student details to OAP:", error);
    throw error;
  }

  // Override IDs with OAP-supplied applicationId
};
export const getOAPSections = async () => {
  try {
    if (!brand || !mode) {
      await logError(
        loggerEnum.Event.MISSING_BRAND_MODE,
        "Brand or mode not set for OAP sections query",
        { brand, mode }
      );
      throw new Error("Brand and mode are required for OAP sections query");
    }

    const params = {
      TableName: `gus-oap-form-sections-${process.env.STAGE}`,
      KeyConditionExpression: "PK = :pk",
      ExpressionAttributeValues: {
        ":pk": `${brand}_${mode}`,
      },
    };

    const result = await dynamoDB.query(params).promise();

    if (!result.Items) {
      await logError(
        loggerEnum.Event.NO_SECTIONS_FOUND,
        "No form sections found in DynamoDB",
        {
          tableName: params.TableName,
          pk: `${brand}_${mode}`,
        }
      );
      return [];
    }

    await logInfo(
      loggerEnum.Event.OAP_SECTIONS_RETRIEVED,
      "OAP sections retrieved successfully",
      {
        sectionsCount: result.Items.length,
        brand,
        mode,
      },
      {}
    );

    return result.Items;
  } catch (error) {
    await logError(
      loggerEnum.Event.GET_OAP_SECTIONS_ERROR,
      "Error retrieving OAP sections",
      {
        brand,
        mode,
        error: error.message || error,
      }
    );
    throw error;
  }
};
export const getObjectDetailsByOppId = async () => {
  try {
    if (!oppId) {
      await logError(
        loggerEnum.Event.MISSING_OPPORTUNITY_ID,
        "Opportunity ID is required but not set"
      );
      throw new Error("Opportunity ID is required");
    }

    const request = {
      object: "getAllObjects",
      customConditions: {
        Id: oppId,
      },
      entityName: "Opportunity",
    };

    await logInfo(
      loggerEnum.Event.SALESFORCE_REQUEST_INITIATED,
      "Salesforce request initiated",
      { oppId, request }
    );

    const details = await postData(`gus/sobject`, request, getUnfcApiKey());

    if (!details || !Array.isArray(details) || details.length === 0) {
      await logError(
        loggerEnum.Event.SALESFORCE_DETAILS_NOT_FOUND,
        "No Salesforce data found for opportunity",
        { oppId }
      );
      throw new Error(`No Salesforce data found for opportunity ${oppId}`);
    }

    const transformedData = transformData(details[0]);

    await logInfo(
      loggerEnum.Event.SF_DATA_RETRIEVED,
      "Salesforce data retrieved and transformed",
      {
        oppId,
        hasOpportunity: !!(transformedData as any).Opportunity,
        hasAccount: !!(transformedData as any).Account,
      },
      {},
      transformedData
    );

    return transformedData;
  } catch (error) {
    await logError(
      loggerEnum.Event.GET_SF_OBJECT_ERROR,
      "Error retrieving Salesforce object details",
      {
        oppId,
        error: error.message || error,
      }
    );
    throw error;
  }
};
export const getExistingApplicationDetails = async () => {
  try {
    if (!email) {
      await logError(
        loggerEnum.Event.MISSING_EMAIL,
        "Email is required for retrieving existing application details"
      );
      throw new Error(
        "Email is required for retrieving existing application details"
      );
    }

    if (!existingApplicationId) {
      await logInfo(
        loggerEnum.Event.NO_EXISTING_APPLICATION_ID,
        "No existing application ID found, returning null",
        { email }
      );
      return null;
    }

    const params = {
      TableName: `gus-oap-student-applications-${process.env.STAGE}`,
      Key: {
        PK: email,
        SK: `UNFC_${existingApplicationId}`,
      },
    };

    await logInfo(
      loggerEnum.Event.RETRIEVING_EXISTING_APPLICATION,
      "Retrieving existing application details",
      params
    );

    const studentDetailsResponse = await dynamoDB.get(params).promise();
    const studentDetails = studentDetailsResponse?.Item;

    if (studentDetails) {
      await logInfo(
        loggerEnum.Event.EXISTING_APPLICATION_FOUND,
        "Existing application details found",
        existingApplicationId,
        {},
        studentDetails
      );
    } else {
      await logInfo(
        loggerEnum.Event.NO_EXISTING_APPLICATION_FOUND,
        "No existing application details found",
        {
          email,
          existingApplicationId,
        }
      );
    }

    return studentDetails || null;
  } catch (error) {
    await logError(
      loggerEnum.Event.GET_EXISTING_APPLICATION_ERROR,
      "Error retrieving existing application details",
      {
        email,
        existingApplicationId,
        error: error.message || error,
      }
    );
    throw error;
  }
};
/**
 * Process form sections and map data from Salesforce response and student details
 * @param sections - Form sections configuration
 * @returns Processed form data
 */
async function processFormSections(sections: any) {
  let result = {};

  // Ensure sections is iterable
  if (!sections || !Array.isArray(sections)) {
    await logError(
      loggerEnum.Event.INVALID_EVENT_STRUCTURE,
      "Sections must be an array",
      { sections }
    );
    return result;
  }

  for (let section of sections) {
    const { fieldName, type, fieldDisplayName } = section;
    switch (type) {
      case "text":
      case "email":
      case "date":
      case "multilineText":
      case "radioButton":
      case "checkbox":
        const textFieldValue = await processTextField(fieldName);
        if (
          textFieldValue !== null &&
          textFieldValue !== undefined &&
          textFieldValue !== ""
        ) {
          result[fieldName] = textFieldValue;
        }
        break;

      case "number":
        result[fieldName] = await processPhoneNumber(
          `${fieldName}.numberWithCode`
        );
        break;

      case "document":
        const docReponse = await processDocument(section);
        result[fieldName] = await cloneObject(docReponse);
        documents.push(...result[fieldName]);
        break;

      case "pickList":
        const reponse = await processPicklist(section);
        result[fieldName] = reponse?.["value"];
        result[fieldDisplayName] = reponse?.["label"];
        break;

      case "subsection":
        if (section.sectionCanRepeat) {
          result[fieldName] = await processRepeatingSubsection(section);
        } else if (section.sectionCanRepeat === false) {
          const subsectionReponse = await processFormSections(
            section[section.subSection]?.fieldData
          );
          result = { ...result, ...subsectionReponse };
        }
        break;

      default:
      // console.warn(`Unknown field type: ${type} for field: ${fieldName}`);
    }
  }
  return result;
}
async function cloneObject(documents: any[]) {
  try {
    if (!documents || !Array.isArray(documents)) {
      await logInfo(
        loggerEnum.Event.NO_DOCUMENTS_TO_CLONE,
        "No documents provided for cloning",
        { documents }
      );
      return [];
    }

    await logInfo(
      loggerEnum.Event.CLONING_DOCUMENTS_STARTED,
      "Starting document cloning process",
      {
        documentsCount: documents.length,
      }
    );

    let result = [];
    for (let i = 0; i < documents.length; i++) {
      const doc = documents[i];

      try {
        if (!doc.documentName || !doc.documentType) {
          await logError(
            loggerEnum.Event.INVALID_DOCUMENT_DATA,
            "Document missing required fields",
            {
              doc,
              index: i,
              hasDocumentName: !!doc.documentName,
              hasDocumentType: !!doc.documentType,
            }
          );
          continue; // Skip invalid documents
        }

        const document = {
          documentFormat: await getFileExtension(doc.documentName),
          documentId: uuidv4(),
          oapName: "UNFC",
          businessUnitFilter: "UNFC",
          email,
          applicationId,
          existingObjectKey: doc.path,
          documentName: doc.documentName,
          documentType: doc.documentType,
        };

        result.push(document);

        await postOAPData(
          `oap/uploadstudentdocument`,
          document,
          getUnfcApiKey()
        );
        // await retryApiCall(
        //   () => postOAPData(
        //     `oap/uploadstudentdocument`,
        //     document,
        //     process.env.HZU_KEY
        //   ),
        //   loggerEnum.Event.UPLOAD_STUDENT_DOCUMENT
        // );

        await logInfo(
          loggerEnum.Event.UPLOAD_DOCUMENT,
          "Document cloned and uploaded successfully",
          {
            documentId: document.documentId,
            documentName: document.documentName,
            documentType: document.documentType,
          }
        );
      } catch (docError) {
        await logError(
          loggerEnum.Event.DOCUMENT_CLONE_ERROR,
          `Error cloning document ${i}`,
          {
            doc,
            index: i,
            error: docError,
          }
        );
        // Continue with next document instead of failing entire process
      }
    }

    await logInfo(
      loggerEnum.Event.CLONING_DOCUMENTS_COMPLETED,
      "Document cloning process completed",
      {
        totalDocuments: documents.length,
        successfullyCloned: result.length,
        failed: documents.length - result.length,
      }
    );

    return result;
  } catch (error) {
    await logError(
      loggerEnum.Event.CLONE_OBJECT_ERROR,
      "Error in document cloning process",
      {
        documents,
        error: error.message || error,
      }
    );
    throw error;
  }
}
async function getFileExtension(fileName): Promise<string> {
  const parts = fileName.split(".");
  if (parts.length === 1) {
    return "";
  }
  return parts[parts.length - 1];
}
/**
 * Process text fields (text, string, date, radioButton)
 */
async function processTextField(fieldName) {
  // Check in Salesforce config for mapping
  const sfMapping = await _findSalesforceMapping(fieldName, mainConfig);
  if (sfMapping) {
    const { objectName, sfFieldName } = sfMapping;

    // Check if data exists in SF response
    if (
      sfResponse[objectName] &&
      sfResponse[objectName][sfFieldName] !== undefined &&
      sfResponse[objectName][sfFieldName] !== null
    ) {
      return sfResponse[objectName][sfFieldName];
    } else {
      // console.log(fieldName, "not found in SF response", studentDetails?.[fieldName]);
    }
  }
  // Fallback to student details
  return studentDetails?.[fieldName] || null;
}

/**
 * Process phone number fields
 */
async function processPhoneNumber(fieldName) {
  const sfMapping = await _findSalesforceMapping(fieldName, mainConfig);

  if (sfMapping) {
    const { objectName, sfFieldName } = sfMapping;

    if (sfResponse[objectName] && sfResponse[objectName][sfFieldName]) {
      return {
        numberWithCode: sfResponse[objectName][sfFieldName],
      };
    }
  }
  const value = getValueFromPath(studentDetails, fieldName);
  if (value) {
    return {
      numberWithCode: value,
    };
  }
  // Check for phone number in student details
  if (studentDetails?.[fieldName]) {
    return {
      numberWithCode: studentDetails[fieldName],
    };
  }

  // Check for formatted phone number
  if (studentDetails?.[`${fieldName}.numberWithCode`]) {
    return {
      numberWithCode: studentDetails[`${fieldName}.numberWithCode`],
    };
  }

  return null;
}

/**
 * Process document fields
 */
async function processDocument(section) {
  const { documentType } = section;

  if (
    !sfResponse.OpportunityFile__c ||
    !Array.isArray(sfResponse.OpportunityFile__c)
  ) {
    return [];
  }

  // Safely filter and map if OpportunityFile__c is an array
  if (Array.isArray(sfResponse.OpportunityFile__c)) {
    return sfResponse.OpportunityFile__c.filter(
      (file) => file.DocumentType__c === documentType
    ).map((file) =>
      mapFields(
        { ...file, ApplicationId__c: applicationId },
        subObjectConfig.documents
      )
    );
  } else {
    await logError(
      loggerEnum.Event.INVALID_EVENT_STRUCTURE,
      "OpportunityFile__c is not an array",
      {
        sfResponse: sfResponse?.OpportunityFile__c,
      }
    );
    return [];
  }
}
type FieldMapping = {
  [key: string]: string | string[];
};

function mapFields(
  object: Record<string, any>,
  config: FieldMapping
): Record<string, any> {
  const mappedDocument: Record<string, any> = {};

  for (const [configField, sfFieldMapping] of Object.entries(config)) {
    if (Array.isArray(sfFieldMapping)) {
      for (const sfField of sfFieldMapping) {
        if (object[sfField] !== undefined && object[sfField] !== null) {
          mappedDocument[configField] = object[sfField];
          break;
        }
      }
      if (mappedDocument[configField] === undefined) {
        mappedDocument[configField] = null;
      }
    } else {
      mappedDocument[configField] =
        object[sfFieldMapping] !== undefined ? object[sfFieldMapping] : null;
    }
  }

  return mappedDocument;
}
/**
 * Process picklist fields
 */
async function processPicklist(section) {
  let {
    fieldName,
    fieldDisplayName,
    pickListValues,
    picklistSourceType,
    picklistSource,
  } = section;
  if (picklistSourceType === "API") {
    pickListValues = await getData(
      replacePlaceholders(picklistSource, studentDetails),
      getUnfcApiKey()
    );
  }

  // Check both fieldName and fieldDisplayName in Salesforce config
  const fieldNameMapping = await _findSalesforceMapping(fieldName, mainConfig);
  const displayNameMapping = fieldDisplayName
    ? await _findSalesforceMapping(fieldDisplayName, mainConfig)
    : null;
  let selectedValue = null;
  let selectedDisplayName = null;

  // Try to get value from Salesforce response
  if (fieldNameMapping) {
    const { objectName, sfFieldName } = fieldNameMapping;
    if (
      sfResponse[objectName] &&
      sfResponse[objectName][sfFieldName] !== undefined
    ) {
      selectedValue = sfResponse[objectName][sfFieldName];
    }
  }

  // Try to get display name from Salesforce response
  if (displayNameMapping) {
    const { objectName, sfFieldName } = displayNameMapping;
    if (
      sfResponse[objectName] &&
      sfResponse[objectName][sfFieldName] !== undefined
    ) {
      selectedDisplayName = sfResponse[objectName][sfFieldName];
    }
  }

  // If both are available from SF, use them
  if (selectedValue && selectedDisplayName) {
    return {
      value: selectedValue,
      label: selectedDisplayName,
      displayName: selectedDisplayName,
    };
  }

  // If only one is available from SF, try to find the other from picklist mapping
  if (
    selectedValue &&
    !selectedDisplayName &&
    pickListValues &&
    Array.isArray(pickListValues)
  ) {
    const matchingOption = pickListValues.find(
      (option) => option.value === selectedValue
    );
    if (matchingOption) {
      return {
        value: selectedValue,
        label: matchingOption.label,
        displayName: matchingOption.label,
      };
    }
  }

  if (
    selectedDisplayName &&
    !selectedValue &&
    pickListValues &&
    Array.isArray(pickListValues)
  ) {
    const matchingOption = pickListValues.find(
      (option) => option.label === selectedDisplayName
    );
    if (matchingOption) {
      return {
        value: matchingOption.value,
        label: selectedDisplayName,
        displayName: selectedDisplayName,
      };
    }
  }

  // If we have one value from SF but no matching picklist option, return what we have
  if (selectedValue || selectedDisplayName) {
    return {
      value: selectedValue || selectedDisplayName,
      label: selectedDisplayName || selectedValue,
      displayName: selectedDisplayName || selectedValue,
    };
  }

  // Fallback to student details if nothing found in SF
  const studentValue = studentDetails[fieldName];
  const studentDisplayName = fieldDisplayName
    ? studentDetails[fieldDisplayName]
    : null;

  // If both are available from student details
  if (studentValue && studentDisplayName) {
    return {
      value: studentValue,
      label: studentDisplayName,
      displayName: studentDisplayName,
    };
  }

  // If only one is available from student details, try to find the other from picklist mapping
  const availableStudentValue = studentValue || studentDisplayName;
  if (
    availableStudentValue &&
    pickListValues &&
    Array.isArray(pickListValues)
  ) {
    const matchingOption = pickListValues.find(
      (option) =>
        option.value === availableStudentValue ||
        option.label === availableStudentValue
    );

    if (matchingOption) {
      return {
        value: matchingOption.value,
        label: matchingOption.label,
        displayName: matchingOption.label,
      };
    }
  }

  // If we have a value from student details but no matching picklist option
  if (availableStudentValue) {
    return {
      value: availableStudentValue,
      label: availableStudentValue,
      displayName: availableStudentValue,
    };
  }

  // No value found anywhere
  return null;
}
/**
 * Process identity document fields from Salesforce to application format
 */
function processIdentityDocument() {
  if (
    !sfResponse.IdentityInfoRecord__c ||
    !Array.isArray(sfResponse.IdentityInfoRecord__c)
  ) {
    return null;
  }

  const identityRecord = sfResponse.IdentityInfoRecord__c[0];
  if (!identityRecord) return null;

  // Map identity fields to application fields
  if (identityRecord.IdentityType__c === "Passport") {
    return {
      passportNumber: identityRecord.IdentityNumber__c,
      passportIssueDate: identityRecord.IdentityIssueDate__c,
      passportExpiryDate: identityRecord.IdentityExpiryDate__c,
      passportIssuingCountryDisplayName:
        identityRecord.IdentityIssuingCountry__c,
    };
  }

  return null;
}

/**
 * Process connections (emergency contacts) from Salesforce to application format
 */
function processConnections() {
  if (!sfResponse.Connection__c || !Array.isArray(sfResponse.Connection__c)) {
    return null;
  }

  const emergencyContact = sfResponse.Connection__c.find(
    (conn) => conn.ConnectionType__c === "Emergency"
  );
  if (!emergencyContact) return null;

  return {
    emergencyContactFirstName: emergencyContact.FirstName__c,
    emergencyContactLastName: emergencyContact.LastName__c,
    emergencyContactRelationship: emergencyContact.Relationship__c,
    emergencyContactPhoneNumber: {
      numberWithCode: emergencyContact.Phone_c__c,
    },
    emergencyContactEmail: emergencyContact.Email__c,
  };
}

/**
 * Process language proficiency from Salesforce to application format
 */
function processLanguageProficiency() {
  if (
    !sfResponse.LanguageProficiencyRecord__c ||
    !Array.isArray(sfResponse.LanguageProficiencyRecord__c)
  ) {
    return null;
  }

  const langRecord = sfResponse.LanguageProficiencyRecord__c[0];
  if (!langRecord) return null;

  return {
    proficiencyQualification: langRecord.ProficiencyQualification__c,
    testName: langRecord.TestProvider__c,
    testDate: langRecord.TestDate__c,
    overallScore: langRecord.TestScore__c,
    listeningScore: langRecord.Listening_Score__c,
    speakingScore: langRecord.Speaking_Score__c,
    readingScore: langRecord.Reading_Score__c,
    writingScore: langRecord.Writing_Score__c,
    testReportFormNo: langRecord.Test_link_Certification__c,
  };
}



/**
 * Process address information from Salesforce to application format
 */
function processAddressInformation() {
  if (!sfResponse?.Account && !sfResponse?.Opportunity) {
    return null;
  }

  const account = sfResponse.Account;
  const opportunity = sfResponse.Opportunity;

  const result = {};

  // Process current address from Account
  if (account) {
    if (account.PersonMailingStreet) {
      result['streetAddress'] = account.PersonMailingStreet;
    }
    if (account.PersonMailingCity || account.gaconnector_City__c) {
      result['city'] = account.PersonMailingCity || account.gaconnector_City__c;
    }
    if (account.PersonMailingPostalCode) {
      result['postalCode'] = account.PersonMailingPostalCode;
    }
    if (account.Country__c || account.PersonMailingCountry) {
      result['countryDisplayName'] = account.Country__c || account.PersonMailingCountry;
    }

    // Process permanent address from Account
    if (account.ShippingStreet) {
      result['permanentStreetAddress'] = account.ShippingStreet;
    }
    if (account.ShippingCity) {
      result['permanentCity'] = account.ShippingCity;
    }
    if (account.ShippingCountry) {
      result['permanentCountryDisplayName'] = account.ShippingCountry;
    }
    if (account.PersonMailingPostalCode) {
      result['permanentPostalCode'] = account.PersonMailingPostalCode;
    }
  }

  // Process additional address info from Opportunity
  if (opportunity) {
    if (opportunity.gaconnector_City__c && !result['permanentCity']) {
      result['permanentCity'] = opportunity.gaconnector_City__c;
    }
  }

  return Object.keys(result).length > 0 ? result : null;
}

/**
 * Process visa application from Salesforce to application format
 */
function processVisaApplication() {
  if (
    !sfResponse.Visa_Application__c ||
    !Array.isArray(sfResponse.Visa_Application__c)
  ) {
    return null;
  }

  const visaRecord = sfResponse.Visa_Application__c[0];
  if (!visaRecord) return null;

  return {
    visaRequirement: visaRecord.Visa_Required__c === true? "Yes" : "No",
    visaNumber: visaRecord.visa_Number__c,
  };
}

/**
 * Process miscellaneous details from Salesforce to application format
 */
async function processMiscDetails() {
  try {
    if (!sfResponse?.Opportunity) {
      await logInfo(
        loggerEnum.Event.NO_OPPORTUNITY_DATA,
        "No opportunity data found for misc details processing"
      );
      return null;
    }

    if (!sfResponse.Opportunity.Application_Misc_Details__c) {
      await logInfo(
        loggerEnum.Event.NO_MISC_DETAILS,
        "No misc details found in opportunity data"
      );
      return null;
    }

    const miscDetails = JSON.parse(
      sfResponse.Opportunity.Application_Misc_Details__c
    );

    const result = {
      coopWorkPermitRequired: miscDetails.Coop_WorkPermitRequired__c,
      coopWorkPermitNumber: miscDetails.Coop_WorkPermitNumber__c,
      coopWorkPermitIssueDate: miscDetails.Coop_WorkPermitIssueDate__c,
      coopWorkPermitExpiryDate: miscDetails.Coop_WorkPermitExpiryDate__c,
      hasDisability: miscDetails.Has_Disability__c,
      pleaseSpecifyDisability: miscDetails.Disability__c,
      isSuspended: miscDetails.EducationHistory_Expelled__c,
      suspensionReason: miscDetails.EducationHistoryComments__c,
      programOfStudy: miscDetails.TransferCredits__c,
      pleaseSpecify: miscDetails.Comments__c,
      province: miscDetails.Province__c,
      permanentProvince: miscDetails.Permanent_Province__c,
      otherTestName: miscDetails.TestName__c,
    };

    await logInfo(
      loggerEnum.Event.MISC_DETAILS_PROCESSED,
      "Misc details processed successfully",
      {
        hasData: Object.values(result).some(
          (val) => val !== null && val !== undefined
        ),
      }
    );

    return result;
  } catch (error) {
    await logError("MISC_DETAILS_PARSE_ERROR", "Error parsing misc details", {
      miscDetailsRaw: sfResponse?.Opportunity?.Application_Misc_Details__c,
      error: error.message || error,
    });
    return null;
  }
}

/**
 * Process field mapping and set default values after misc details processing
 *
 * Field Mappings (only if target field has no value):
 * - passportName → firstName
 * - passportMiddleName → middleName
 * - passportLastName → lastName
 * - aggreditationSignature → submissionSignature
 * - enrollmentSignature → submissionSignature
 * - consumerInfosignature → submissionSignature
 *
 * Default Values:
 * - aggreditationDate, consumerInfodate, submittedDate → current date
 * - internationalStudentAcknowledgement1/2/3, consumerInfoAcknowledgement → true
 */
async function processFieldMappingAndDefaults(data: any) {
  try {
    const result = { ...data };

    await logInfo(
      loggerEnum.Event.DATA_PROCESSING_COMPLETE,
      "Starting field mapping and default value assignment",
      {
        hasPassportName: !!result.passportName,
        hasFirstName: !!result.firstName,
        hasAggreditationSignature: !!result.aggreditationSignature,
      }
    );

    // Field mapping: if target field has no value, map from source field
    const fieldMappings = [
      { source: 'firstName', target: 'passportFirstName' },
      { source: 'middleName', target: 'passportMiddleName' },
      { source: 'lastName', target: 'passportLastName' },
      { source: 'submissionSignature', target: 'aggreditationSignature' },
      { source: 'submissionSignature', target: 'enrollmentSignature' },
      { source: 'submissionSignature', target: 'consumerInfosignature' },
      { source: 'submittedDate', target: 'aggreditationDate' },
      { source: 'submittedDate', target: 'consumerInfodate' }
    ];

    // Apply field mappings only if target field is empty/null/undefined
    for (const mapping of fieldMappings) {
      if (!result[mapping.target] && result[mapping.source]) {
        result[mapping.target] = result[mapping.source];
      }
    }

    // Handle permanent address mapping (similar to UNFC service logic)
    // If permanent address is not the same as current address, copy current address to permanent
    if (!result.isPermanentAddressSame || result.isPermanentAddressSame === "No") {
      if (result.streetAddress && !result.permanentStreetAddress) {
        result.permanentStreetAddress = result.streetAddress;
      }
      if (result.city && !result.permanentCity) {
        result.permanentCity = result.city;
      }
      if (result.postalCode && !result.permanentPostalCode) {
        result.permanentPostalCode = result.postalCode;
      }
      if (result.country && !result.permanentCountry) {
        result.permanentCountry = result.country;
      }
      if (result.countryDisplayName && !result.permanentCountryDisplayName) {
        result.permanentCountryDisplayName = result.countryDisplayName;
      }
    }

    // Set default boolean acknowledgements
    const acknowledgementFields = [
      'internationalStudentAcknowledgement1',
      'internationalStudentAcknowledgement2',
      'internationalStudentAcknowledgement3',
      'consumerInfoAcknowledgement'
    ];

    for (const field of acknowledgementFields) {
      if (result[field] === undefined || result[field] === null) {
        result[field] = true;
      }
    }

    await logInfo(
      loggerEnum.Event.DATA_PROCESSING_COMPLETE,
      "Field mapping and default value assignment completed",
      {
        mappedFields: fieldMappings.length,
        acknowledgementFieldsSet: acknowledgementFields.length
      }
    );

    return result;
  } catch (error) {
    await logError(
      loggerEnum.Event.DEFERRAL_PROCESSING_FAILED,
      "Error during field mapping and default value assignment",
      {
        error: error.message || error,
        data: data
      }
    );
    throw error;
  }
}

/**
 * Convert boolean values back to Yes/No strings
 */
function convertBooleanToYesNo(data) {
  try {
    const keys = [
      "isCurrentEmployee",
      "isEmployerOfferTuition",
      "isFirstGenerationCollegeStudent",
      "haveWorkExperience",
      "haveTrafficViolation",
    ];

    const result = { ...data };

    for (const key of keys) {
      if (typeof result[key] === "boolean") {
        result[key] = result[key] ? "Yes" : "No";
      }
    }

    // Handle nested objects like priorInstitutionsAttended
    if (
      result.priorInstitutionsAttended &&
      Array.isArray(result.priorInstitutionsAttended)
    ) {
      result.priorInstitutionsAttended = result.priorInstitutionsAttended.map(
        (institution) => {
          if (typeof institution.canShareTransferringCredits === "boolean") {
            institution.canShareTransferringCredits =
              institution.canShareTransferringCredits ? "Yes" : "No";
          }
          return institution;
        }
      );
    }

    return result;
  } catch (error) {
    console.error("Error converting boolean to Yes/No:", error);
    return data;
  }
}

/**
 * Process repeating subsections
 */
async function processRepeatingSubsection(section) {
  const { fieldName } = section;

  if (fieldName === "priorInstitutionsAttended") {
    if (
      !sfResponse.EducationHistoryRecord__c ||
      !Array.isArray(sfResponse.EducationHistoryRecord__c)
    ) {
      return [];
    }
    subObjectConfig.educationHistory.institutionTypeDisplayName =
      "InstitutionName__c";

    // Use Promise.all to wait for all async operations to complete
    // Safely map if EducationHistoryRecord__c is an array
    if (!Array.isArray(sfResponse.EducationHistoryRecord__c)) {
      await logError(
        loggerEnum.Event.INVALID_EVENT_STRUCTURE,
        "EducationHistoryRecord__c is not an array",
        {
          sfResponse: sfResponse?.EducationHistoryRecord__c,
        }
      );
      return [];
    }

    const educationRecords = await Promise.all(
      sfResponse.EducationHistoryRecord__c.map(async (record) => {
        const mappedEduRecord = mapFields(record, {
          ...subObjectConfig.educationHistory,
          earnedDegree: "Degree_earned__c",
        });

        // Get certificates/documents related to this education record
        const docResponse = sfResponse.OpportunityFile__c.filter(
          (file) => file.Related_Education_History__c === record.Id
        ).map((file) =>
          mapFields(
            { ...file, ApplicationId__c: applicationId },
            subObjectConfig.documents
          )
        );

        mappedEduRecord["institutionCertificates"] = await cloneObject(
          docResponse
        );
        return mappedEduRecord;
      })
    );

    return educationRecords;
  }

  return [];
}
// The original logic, renamed to _findSalesforceMapping
async function _findSalesforceMapping(fieldName, mainConfig) {
  for (const [objectName, fields] of Object.entries(mainConfig)) {
    if (typeof fields === "object") {
      for (const [configFieldName, sfFieldName] of Object.entries(fields)) {
        if (configFieldName === fieldName) {
          return {
            objectName,
            sfFieldName: Array.isArray(sfFieldName)
              ? sfFieldName[0]
              : sfFieldName,
          };
        }
      }
    }
  }
  return null;
}

function transformData(data) {
  const keyMapping = {
    Connections__r: "Connection__c",
    OpportunityTeamMembers: "OpportunityTeamMember",
    EducationHistoryRecords__r: "EducationHistoryRecord__c",
    IdentityInfoRecords__r: "IdentityInfoRecord__c",
    OpportunityFiles__r: "OpportunityFile__c",
    WorkHistoryRecords__r: "WorkHistoryRecord__c",
    LanguageProficiencyRecords__r: "LanguageProficiencyRecord__c",
    Applications__r: "Application__c",
  };

  const result = {};

  for (const key in data) {
    const value = data[key];
    const newKey = keyMapping[key] || key;

    if (value && typeof value === "object" && Array.isArray(value.records)) {
      result[newKey] = value.records;
    } else if (typeof value === "object") {
      result[newKey] = value;
    } else {
      if (!result["Opportunity"]) {
        result["Opportunity"] = {};
      }
      result["Opportunity"][newKey] = value;
    }
  }

  return result;
}
function replacePlaceholders(
  template: string,
  values: Record<string, any>
): string {
  return template.replace(/\$\{(\w+)\}/g, (_, key) => {
    return values[key] !== undefined ? values[key] : "";
  });
}
function getValueFromPath(obj, path) {
  return path.split(".").reduce((acc, key) => acc?.[key], obj);
}
