import { handlerPath } from "@libs/handler-resolver";

export const unfcSfQueue = {
    handler: `${handlerPath(__dirname)}/unfcHandler.handleUnfcSfRequests`,
    name: 'unfc-sf-sqs-${self:provider.stage}',
    events: [
        {
            sqs: '${self:provider.environment.UNFC_AGENT_EIP_QUEUE}',
        }
    ],
    timeout: 180
}

export const unfcChangeRequestQueue = {
  handler: `${handlerPath(
    __dirname
  )}/unfcChangeRequestHandler.handleUnfcChangeRequests`,
  name: "unfc-change-request-sqs-${self:provider.stage}",
  events: [
    {
      sqs: {
        arn: 'arn:aws:sqs:eu-west-1:${aws:accountId}:${self:custom.variables.stagePrefix.${self:provider.stage}}-UNFC-EIP-CR-QUEUE.fifo',
      },
    },
  ],
  timeout: 180,
};


