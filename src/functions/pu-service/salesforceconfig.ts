export const salesforceAgentSubObjectsConfig = {
  documents: {
    applicationId: "ApplicationId__c",
    documentType: "DocumentType__c",
    documentName: ["Name", "OriginalValue__c", "FilePath__c"],
    opportunityId: "Opportunity__c",
    path: ["S3FileName__c", "FullUrl__c"],
    bucketName: "BucketName__c",
    institutionName: "Additional_Info__c",
    workReference: "Related_Work_History__c",
    testReference: "Related_Language_Proficiency__c",
    identityReference: "Related_Proof_of_Identity__c",
    isIdentityInformationAccurate: "Is_Info_Accurate__c",
  },
  identityDocumentObject: {
    identityNumber: "Identity_Document_Number__c",
    identityIssueDate: "Issue_Date__c",
    identityExpiryDate: "Expiry_Date__c",
    identityIssuingCountry: "Issuing_Country__c",
    identityType: "Identity_Type__c",
  },
  visaApplication: {
    visaRequired: "Visa_Required__c",
    visaNumber: "Visa_Number__c",
  },
  educationHistory: {
    institutionCountryDisplayName: "Country__c",
    institutionDisplayName: "InstitutionName__c",
    gpa: "GPA_Score__c",
    schoolLevel: "Education_Level__c",
    degreePlanned: "Study_completed__c",
    degreeTypeDisplayName: "AwardDegree__c",
    majorTypeDisplayName: "Specialisation__c",
    plannedDegreeType: "AwardDegree__c",
    plannedMajor: "Specialisation__c",
    dateOfGraduation: "GraduationDate__c",
    dateOfEnrolment:"EnrolmentDateYear__c",
  },
  languageProficiency: {
    testName: ["Name", "TestProvider__c", "ProficiencyQualification__c"],
    testTakenDate: "TestDate__c",
    total: "TestScore__c",
    internetBasedTest: "TestScore__c",
    reading: "Reading_Score__c",
    writing: "Writing_Score__c",
    listening: "Listening_Score__c",
    speakingScore: "Speaking_Score__c",
    overallBandScore: "TestScore__c",
    certificateLink: "Test_link_Certification__c"
  },
  workHistory: {
    employmentType: "Employment_Type__c",
    companyName: "Employer__c",
    enterCompanyName: "Employer__c",
    jobFunction: "NatureOfDuties__c",
    recentJobIndustry: "Industry__c",
    recentJobTitle: "Position__c",
    employmentStartDate: "StartDate__c",
    employmentEndDate: "EndDate__c",
  },
  product: {
    productId: "Product2Id",
  },
  connections: {
    recommenderfirstName: "First_Name_c__c",
    recommenderlastName: "Last_Name__c",
    recommenderEmailAddress: "Email_c__c"
  },
};

export const salesforceAgentConfig = {
  LanguageProficiencyRecord__c: ["languageProficiency"],
  IdentityInfoRecord__c: ["identityDocumentObject"],
  Visa_Application__c: ["visaApplication"],
  EducationHistoryRecord__c: ["educationHistory"], 
  WorkHistoryRecord__c: ["workHistory"], 
  Connection__c: ["connections"],
  OpportunityFile__c: ["documents"],
  OpportunityLineItem: ["product"],
  Lead: {
    firstName: "FirstName",
    lastName: "LastName",
    email: "Email",
    "primaryPhoneNumber.numberWithCode": "Phone",
    "phoneNumber.numberWithCode": "Phone",
    countryDisplayName: ["Country__c", "Country"],

    program: "Programme__c",
    location: "Location__c",

    // 🔹 Agent & Ownership
    agentAccountId: "AgentAccount__c",
    agentContactId: "Agent_Contact__c",
    accountManagerUserId: "BusinessDeveloper__c",
    agentContactUserId: "OwnerId",

    // 🔹 Business & Record Metadata
    brand: "Brand__c",
    businessUnit: "BusinessUnit__c",
    businessUnitFilter: "BusinessUnitFilter__c",
    leadRecordTypeId: "RecordTypeId",
  },
  Account: {
    firstName: "FirstName",
    lastName: "LastName",
    email: ["PersonEmail", "Email__c"],
    "phoneNumber.numberWithCode": "Phone",
    countryDisplayName: "PersonMailingCountry",
    reportYourSex: "Gender__c",
    countryOfBirthDisplayName: "CountryOfBirth__c",
    passportFirstName: "FirstName",
    passportLastName: "LastName",
    birthDate: "DateofBirth__c",
    placeOfBirth: "PlaceOfBirth__c",
    middleName: "Middle_Name__c",
    suffix: "LastName",
    formerLastName: "Former_Last_Name__c",
    myFirstName: "PreferedFirstName__c",
    countryOrTerritoryDisplayName: "PersonMailingCountry",
    streetAddress: "PersonMailingStreet",
    city: "PersonMailingCity",
    stateOrProvinceDisplayName: "PersonMailingState",
    // county: "PersonMailingState",
    postalCode: "PersonMailingPostalCode",
    permanentCountryOrTerritoryDisplayName: "PersonMailingCountry",
    permanentStreetAddress: "PersonMailingStreet",
    permanentCity: "PersonMailingCity",
    permanentStateOrProvinceDisplayName: "PersonMailingState",
    // permanentCounty: "PersonMailingState",
    permanentPostalCodeTwo: "PersonMailingPostalCode",
    "primaryPhoneNumber.numberWithCode": "Phone",
    "alternatePhoneNumber.numberWithCode": "PrimaryPhone__c",
    unitedStatesCitizenship: "Citizenship__c",
    countryOfCitizenshipDisplayName: "Citizenship__c",
    secondCountryOfCitizenshipDisplayName: "Dual_Citizenship__c",
    ethnicity: "Ethnicity__c",
    firstLanguage: "Primary_Language__c",
    privacyPolicyConsent: [
      "HasOptedOutOfMarketingEmail__c",
      "HasOptedOutOfGUSMarketingEmail__c",
    ],

    // 🔹 Organizational/Business Info
    businessUnit: "BusinessUnit__c",
    brand: "Brand__c",
    accountRecordTypeId: "RecordTypeId",
    agentContactUserId: "OwnerId",

    // 🔹 Academic & Intake
    programTypeDisplayName: "Level__pc",
  },
  Opportunity: {
    haveUsVisa: "Visa__c",
    typeOfVisa: "VisaType__c",
    issuedCountryDisplayName: "VisaCountry__c",
    validFromDate: "VisaIssueDate__c",
    validUntilDate: "VisaExpiryDate__c",

        // 🔹 Application Details
    appId: "ApplicationId__c",
    applicationId: "ApplicationFormId__c",
    stage: "StageName",
    admissionStage: "AdmissionsStage__c",
    progress: "ApplicationProgress__c",
    isSubmitted: "ApplicationSubmitted__c",

    // 🔹 Program Details
    program: "Programme__c",
    programDisplayName: "Name",
    intake: ["CloseDate", "Product_Intake_Date__c", "OverallStartDate__c"],

    // 🔹 Agent & User Information
    agentContactId: "Agent_Contact__c",
    agentAccountId: "AgentAccount__c",
    accountManagerUserId: "BusinessDeveloper__c",
    agentContactUserId: "OwnerId",

    // 🔹 Record Metadata
    opportunityRecordTypeId: "RecordTypeId",
    businessUnit: "BusinessUnit__c",
    pricebookId: "Pricebook2Id",
  },

  Application__c: {
    firstName: "First_Name__c",
    lastName: "Last_Name__c",
    email: "Email__c",
    "phoneNumber.numberWithCode": "Mobile__c",
    countryDisplayName: "Country__c",
    reportYourSex: "Gender__c",
    countryOfBirthDisplayName: "Country_of_Birth__c",
    birthDate: "Date_of_birth__c",
    placeOfBirth: "Place_of_Birth__c",
    middleName: "Middle_Other_Name_s__c",
    suffix: "Last_Name__c",
    "primaryPhoneNumber.numberWithCode": "Mobile__c",
    "alternatePhoneNumber.numberWithCode": "Alternate_Phone_Number__c",

    // 🔹 Academic Program Details
    program: "Programme__c",
    programDisplayName: "Program_Of_Study__c",
    programType: "Level_Of_Study__c",
    intake: ["Start_Date__c", "Intake__c"],

    // 🔹 Agent & Ownership
    agentContactUserId: "OwnerId",
    accountManagerUserId: "Business_Developer__c",
    agentAccountId: "Agent_Account__c",
    agentContactId: "Agent_Contact__c",

    // 🔹 Application Metadata
    applicationId: "Application_Form_Id__c",
    applicationRecordTypeId: "RecordTypeId",
  },
};
