export const salesforceAgentSubObjectsConfig = {
  documents: {
    applicationId: "ApplicationId__c",
    documentType: "DocumentType__c",
    documentName: ["Name", "OriginalValue__c", "FilePath__c"],
    opportunityId: "Opportunity__c",
    path: ["S3FileName__c", "FullUrl__c"],
    bucketName: "BucketName__c",
    institutionName: "Additional_Info__c",
    workReference: "Related_Work_History__c",
    eduReference: "Related_Education_History__c",
    testReference: "Related_Language_Proficiency__c",
    identityReference: "Related_Proof_of_Identity__c",
    isIdentityInformationAccurate: "Is_Info_Accurate__c",
  },
  educationHistory: {
    institutionCountryDisplayName: "Country__c",
    institutionNameDisplayName: "InstitutionName__c",
    qualificationName: "Name",
    subject: "Specialisation__c",
    isQualificationCompleted: "Study_completed__c",
    grade: "AverageGrade__c",
    monthCompleted: "GraduationDate__c",
    expectedMonthCompleted: "GraduationDate__c",
    enrollmentDateYear:"EnrolmentDateYear__c",
  },
  languageProficiency: {
    isEnglishFirstLanguage: "ProficiencyQualification__c",
    englishQualificationType: ["Name","ProficiencyQualification__c"],
    englishTestScore: "TestScore__c",
    englishMediumYearsDisplayName: "Years_of_Experience__c",
    englishCertificationNo: "Test_link_Certification__c",
  },
  workHistory: {
    company: "Employer__c",
    department: "NatureOfDuties__c",
    industry: "Other_Industry__c",
    duties: "Position__c",
    employmentStatus: "Employment_Type__c",
    startDate: "StartDate__c",
    endDate: "EndDate__c",
  },
  teamMembers: {
    opportunityAccessLevel: "OpportunityAccessLevel",
    teamMemberRole: "TeamMemberRole",
    userId: "UserId",
  },
  identityDocumentObject: {
    identityNumber: "Identity_Document_Number__c",
    identityIssueDate: "Issue_Date__c",
    identityExpiryDate: "Expiry_Date__c",
    identityType: "Identity_Type__c",
    identityIssuingCountry: "Issuing_Country__c",
  },
  connections: {
    refereeFirstName: "First_Name_c__c",
    refereeLastName: "Last_Name__c",
    refereePosition: "Job_position__c",
    refereeInstitution: "Company__c",
    refereeEmail: "Email_c__c",
    refereeCountryDisplayName: "Address_c__c",
    "refereePhone.numberWithCode": "Phone_c__c",
    connectionType: "Type__c",
  },
  visaApplication: {
    visaRequired: "Visa_Required__c",
  },
};

export const salesforceAgentConfig = {
  IdentityInfoRecord__c: ["identityDocumentObject"], // passport
  LanguageProficiencyRecord__c: ["languageProficiency"], //test
  EducationHistoryRecord__c: ["educationHistory"], //qualification
  WorkHistoryRecord__c: ["workHistory"], //experi
  OpportunityFile__c: ["documents"], // all file
  Connection__c: ["connections"], //refer
  Lead: {
    // 🔹 Personal Information
    firstName: "FirstName",
    lastName: "LastName",
    email: "Email",

    // 🔹 Contact Information
    "mobilePhone.numberWithCode": "Phone",
    mailingState: "State__c",
    countryDisplayName: ["Country", "Country__c"],

    // 🔹 Academic Program Details
    program: "Programme__c",
    location: "Location__c",

    // 🔹 Agent & Ownership
    agentAccountId: "AgentAccount__c",
    agentContactId: "Agent_Contact__c",
    accountManagerUserId: "BusinessDeveloper__c",
    agentContactUserId: "OwnerId",

    // 🔹 Business & Record Metadata
    brand: "Brand__c",
    businessUnit: "BusinessUnit__c",
    businessUnitFilter: "BusinessUnitFilter__c",
    leadRecordTypeId: "RecordTypeId"
  },
  Account: {
    // 🔹 Personal Details
    email: "PersonEmail",
    firstName: "FirstName",
    middleName: "Middle_Name__c",
    lastName: "LastName",
    preferredName: "PreferedFirstName__c",
    gender: "Gender__c",
    birthDate: "DateOfBirth__c",
    placeOfBirth: "PlaceOfBirth__c",
    title: "Salutation",
    legalNationalityDisplayName:[ "Nationality__c", "Citizenship__c"],
    dualNationalityDisplayName:"Dual_Citizenship__c",
    citizenshipDisplayName: "Citizenship__c",

    // 🔹 Contact Details
    "mobilePhone.numberWithCode": "Phone",
    "contactNumber.numberWithCode": "PrimaryPhone__c",
    "secondaryMobilePhone.numberWithCode": "PersonOtherPhone",

    // 🔹 Address - Permanent
    streetAddress: "PersonMailingStreet",
    city: ["gaconnector_City__c", "PersonMailingCity"],
    postalCode: "PersonMailingPostalCode",
    country: "PersonMailingCountryCode",
    countryDisplayName: ["Country__c", "PersonMailingCountry"],
    countyDisplayName: "PersonMailingState",

    // 🔹 Address - Correspondence
    correspondenceStreetAddress: "BillingStreet",
    correspondenceCity: "BillingCity",
    correspondencePostalCode: "BillingPostalCode",
    correspondenceCountry: "BillingCountryCode",
    correspondenceCountryDisplayName: "BillingCountry",
    correspondenceCountyDisplayName: "BillingState",

    // 🔹 Academic & Intake
    programTypeDisplayName: "Level__pc",

    // 🔹 Preferences
    correspondenceLanguage: "CommunicationLanguage__c",

    // 🔹 Organizational/Business Info
    businessUnit: "BusinessUnit__c",
    brand: "Brand__c",
    accountRecordTypeId: "RecordTypeId",
    agentContactUserId: "OwnerId",

    // 🔹 Identity Documents
    passportNumber: "Passport__pc",
    ethinicBackgroundDisplayName: "Ethnicity__c",

    // 🔹 Privacy & Marketing Consent
    privacyPolicyConsent: [
      "HasOptedOutOfMarketingEmail__c",
      "HasOptedOutOfGUSMarketingEmail__c",
    ],
  },
  Opportunity: {
    // 🔹 Application Details
    appId: "ApplicationId__c",
    applicationId: "ApplicationFormId__c",
    miscDetails: "Application_Misc_Details__c",
    stage: "StageName",
    admissionStage: "AdmissionsStage__c",
    progress: "ApplicationProgress__c",
    isSubmitted: "ApplicationSubmitted__c",

    // 🔹 Program Details
    program: "Programme__c",
    programDisplayName: "Name",
    intake: ["CloseDate", "Product_Intake_Date__c", "OverallStartDate__c"],
    partnerInstitution: "PathwayProviderId__c",
    disability: "HESADisability__c",
    CriminalConvictions: "CriminalConvictions__c",
    personalStatement: "PersonalStatement__c",

    // 🔹 Contact & Location
    "phoneNumber.numberWithCode": "AccountPhone__c",
    location: "Location__c",
    city: "gaconnector_City__c",
    mailingState: "State__c",
    citizenshipDisplayName: "Citizenship__c",

    // 🔹 Agent & User Information
    agentContactId: "Agent_Contact__c",
    agentAccountId: "AgentAccount__c",
    accountManagerUserId: "BusinessDeveloper__c",
    agentContactUserId: "OwnerId",
    sourceOfFunding:"Funding__c",

    // 🔹 Record Metadata
    opportunityRecordTypeId: "RecordTypeId",
    businessUnit: "BusinessUnit__c",
    pricebookId: "Pricebook2Id",

    // 🔹 Declarations & Consents
    declaration3: "DeclarationInfoProvided__c",

    // 🔹 Source of Application
    opportunityApplicationSource: "ApplicationSource__c",
    visa: "Visa__c",
    visaType:"VisaType__c",
    visaTypeName:"Other_Visa_Name__c",
    
  },
  Application__c: {
    // 🔹 Personal Information
    firstName: "First_Name__c",
    middleName: "Middle_Other_Name_s__c",
    lastName: "Last_Name__c",
    preferredName: "Preferred_Name__c",
    fullName: "Name",
    birthDate: "Date_of_birth__c",
    gender: "Gender__c",
    placeOfBirth: "Place_of_Birth__c",
    legalNationalityDisplayName: "Nationality__c",
    //dualNationalityDisplayName:"Dual_Nationality__c",
    citizenshipDisplayName: "Citizenship__c",
    haveCriminalConvictions: "Do_you_have_any_unspent_criminal_convict__c",

    // 🔹 Contact Information
    email: "Email__c",
    "mobilePhone.numberWithCode": "Mobile__c",
    "contactNumber.numberWithCode": "Primary_Phone_Number__c",
    "secondaryMobilePhone.numberWithCode": "Alternate_Phone_Number__c",

    // 🔹 Address Information
    streetAddress: "Street_Address__c",
    city: ["City__c", "Correspondence_City__c"],
    postalCode: ["PostCode__c", "Correspondence_Postal_Code__c"],
    countryDisplayName: ["Country__c", "Correspondence_Country__c"],

    // 🔹 Academic Program Details
    program: "Programme__c",
    programDisplayName: "Program_Of_Study__c",
    programType: "Level_Of_Study__c",
    intake: ["Start_Date__c", "Intake__c"],
    englishTestScore: "Overall_Score__c",
    englishQualificationType:"Name_of_language_certificate__c",

    

    // 🔹 Agent & Ownership
    agentContactUserId: "OwnerId",
    accountManagerUserId: "Business_Developer__c",
    agentAccountId: "Agent_Account__c",
    agentContactId: "Agent_Contact__c",

    // 🔹 Application Metadata
    applicationId: "Application_Form_Id__c",
    applicationRecordTypeId: "RecordTypeId",

    requireStudentVisa: "Do_you_need_visa_for_this_course__c",
    personalStatement: "Personal_Statement__c",
  },
  OpportunityLineItem: {
    productId: "Product2Id",
  },
  OpportunityTeamMember: ["teamMembers"],
  Visa_Application__c: ["visaApplication"],
};
