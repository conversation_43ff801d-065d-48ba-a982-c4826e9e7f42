const AWS = require("aws-sdk");
import { limTemplate } from "@functions/notification-service/emailtemplates/lim";
import { ibatTemplate } from "@functions/notification-service/emailtemplates/ibat";
import { hzuTemplate } from "@functions/notification-service/emailtemplates/hzu";
import { ucwTemplate } from "@functions/notification-service/emailtemplates/ucw";
import { getS3PdfObject } from "./getS3PdfObject";
import { unfcTemplate } from "./emailtemplates/unfc";
import { lsbfmyrTemplate } from "./emailtemplates/lsbfmyr";
import { uegTemplate } from "./emailtemplates/ueg";
import { uegTemplateDe } from "./emailtemplates/uegde";
import { wulTemplate } from "./emailtemplates/wul";
import { ardTemplate } from "./emailtemplates/ard";
import axios from "axios";
import { puTemplate } from "./emailtemplates/pu";

AWS.config.update({ region: process.env.REGION });

export const sendEmail: any = async (event) => {
  console.log(event);
  const snsMessage = JSON.parse(event.Records[0].Sns.Message);
  if (snsMessage) {
    console.log(snsMessage);
    let {
      emailAddress,
      subject,
      firstName,
      lastName,
      brand,
      translation,
      applicationId,
      businessUnitFilter,
      documentId,
      intakeDate,
      programmeName,
      location,
      agentCompanyName,
      additionalAttachments,
    } = snsMessage;
    let applicationPdfBase64;

    const name = `${documentId}.pdf`;
    const type = "Application form";
    const objectKey = `${applicationId}/${type}/${name}`;
    let bucketName;
    switch (brand || businessUnitFilter) {
      case "UEG":
        bucketName = process.env.CAMPUSNET_BUCKET_NAME;
        break;
      default:
        bucketName = process.env.REVIEW_CENTER_BUCKET_NAME;
    }

    try {
      const data = await getS3PdfObject(
        bucketName,
        objectKey,
        process.env.S3_BUCKET_ACCESS_ROLE_ARN
      );
      applicationPdfBase64 = data.toString("base64");
    } catch (err) {
      console.log("Function call Error[getS3PdfObject]" + err);
      throw err;
    }

    // Fetch additional attachments if present
    const additionalAttachmentsData = [];
    if (additionalAttachments && additionalAttachments.length > 0) {
      for (const attachment of additionalAttachments) {
        try {
          const data = await getS3PdfObject(
            bucketName,
            attachment.objectKey,
            process.env.S3_BUCKET_ACCESS_ROLE_ARN
          );
          additionalAttachmentsData.push({
            base64Data: data.toString("base64"),
            documentId: attachment.documentId,
          });
        } catch (err) {
          console.log(
            `Error fetching additional attachment ${attachment.documentId}: ${err}`
          );
          throw err;
        }
      }
    }

    let htmlEmailTemplate = "";
    let senderEmail;
    let appheroCobrandedUrl = "";
    switch (brand) {
      case "LIM College":
        senderEmail = process.env.SES_SENDER_EMAIL;
        appheroCobrandedUrl = await generateCobrandingLink(emailAddress, brand);
        htmlEmailTemplate = limTemplate;
        htmlEmailTemplate = htmlEmailTemplate.replace(
          "{{Recipient.FirstName}}",
          firstName
        );
        htmlEmailTemplate = htmlEmailTemplate.replace(
          "{{Programme Name}}",
          programmeName
        );
        htmlEmailTemplate = htmlEmailTemplate.replace(
          "{{Intake Date}}",
          intakeDate
        );
        htmlEmailTemplate = htmlEmailTemplate.replace(
          "{{appHeroCobrandingLink}}",
          appheroCobrandedUrl
        );
        break;
      case "LSBFMYR":
        senderEmail = process.env.LSBFMYR_SES_SENDER_EMAIL;
        htmlEmailTemplate = lsbfmyrTemplate;
        htmlEmailTemplate = htmlEmailTemplate.replace(
          "{{Recipient.FirstName}}",
          firstName
        );
        htmlEmailTemplate = htmlEmailTemplate.replace(
          "{{Programme Name}}",
          programmeName
        );
        break;
      case "IBAT":
        senderEmail = process.env.SES_SENDER_EMAIL;
        htmlEmailTemplate = ibatTemplate;
        htmlEmailTemplate = ibatTemplate.replace(
          "{{Recipient.FirstName}}",
          firstName
        );
        break;
      case "HZU":
        senderEmail = process.env.SES_SENDER_EMAIL;
        htmlEmailTemplate = hzuTemplate;
        htmlEmailTemplate = hzuTemplate.replace(
          "{{Recipient.FirstName}}",
          firstName
        );
        break;
      case "UCW":
        senderEmail = process.env.UEG_SES_SENDER_EMAIL;
        appheroCobrandedUrl = await generateCobrandingLink(emailAddress, brand);
        htmlEmailTemplate = ucwTemplate;
        htmlEmailTemplate = htmlEmailTemplate.replace(
          "{{Recipient.FirstName}}",
          firstName
        );
        htmlEmailTemplate = htmlEmailTemplate.replace(
          "{{Recipient.LastName}}",
          lastName
        );
        htmlEmailTemplate = htmlEmailTemplate.replace(
          "{{appHeroCobrandingLink}}",
          appheroCobrandedUrl
        );
        break;
      case "WUL":
        senderEmail = process.env.SES_SENDER_EMAIL;
        htmlEmailTemplate = wulTemplate;
        htmlEmailTemplate = htmlEmailTemplate.replace(
          "{{Recipient.FirstName}}",
          firstName
        );
        htmlEmailTemplate = htmlEmailTemplate.replace(
          "{{Recipient.LastName}}",
          lastName
        );
        break;
      case "UEG":
        senderEmail = process.env.UEG_SES_SENDER_EMAIL;

        // Fetch student data to get localization for UEG brand
        let localization = "en"; // Default to English
        if (translation) {
          localization = translation;
        }

        // Select template based on localization
        htmlEmailTemplate = localization === "de" ? uegTemplateDe : uegTemplate;
        subject =
          localization === "de"
            ? "Bestätigung Ihrer Bewerbung an der UE - University of Europe for Applied Sciences"
            : subject;
        htmlEmailTemplate = htmlEmailTemplate.replace(
          "{{Recipient.FirstName}}",
          firstName
        );
        break;
      case "Arden University":
        senderEmail = process.env.SES_SENDER_EMAIL;
        htmlEmailTemplate = ardTemplate;
        htmlEmailTemplate = htmlEmailTemplate.replace(
          "{{Recipient.FirstName}}",
          firstName
        );
        htmlEmailTemplate = htmlEmailTemplate.replace(
          "{{Recipient.LastName}}",
          lastName
        );
        htmlEmailTemplate = htmlEmailTemplate.replace(
          "{{Programme Name}}",
          programmeName
        );
        htmlEmailTemplate = htmlEmailTemplate.replace(
          "{{Intake Date}}",
          intakeDate
        );
        htmlEmailTemplate = htmlEmailTemplate.replace(
          "{{location}}", 
          location
        );
        htmlEmailTemplate = htmlEmailTemplate.replace(
          "{{agentCompanyName}}",
          agentCompanyName
        );
        break;
      case "PU":
        senderEmail = process.env.SES_SENDER_EMAIL;
        htmlEmailTemplate = puTemplate;

        htmlEmailTemplate = htmlEmailTemplate.replace(
          "{{Recipient.FirstName}}",
          firstName
        );
        htmlEmailTemplate = htmlEmailTemplate.replace(
          "{{Recipient.LastName}}",
          lastName
        );
        htmlEmailTemplate = htmlEmailTemplate.replace(
          "{{Programme Name}}",
          programmeName
        );
        htmlEmailTemplate = htmlEmailTemplate.replace(
          "{{Intake Date}}",
          intakeDate
        );
        htmlEmailTemplate = htmlEmailTemplate.replace("{{location}}", location);
        htmlEmailTemplate = htmlEmailTemplate.replace(
          "{{agentCompanyName}}",
          agentCompanyName
        );
        break;
      case "UNFC":
        senderEmail = process.env.SES_SENDER_EMAIL;
        htmlEmailTemplate = unfcTemplate;
        htmlEmailTemplate = htmlEmailTemplate.replace(
          "{{Recipient.FirstName}}",
          firstName
        );
        htmlEmailTemplate = htmlEmailTemplate.replace(
          "{{Recipient.LastName}}",
          lastName
        );
        break;
    }

    const ses_mail = `From: ${senderEmail}
To: ${emailAddress}
Subject: ${subject}
MIME-Version: 1.0
Content-Type: multipart/mixed; boundary="NextPart"

--NextPart
Content-Type: text/html; charset=utf-8

${htmlEmailTemplate}

--NextPart
Content-Type: application/pdf; name=${applicationId}.pdf
Content-Disposition: attachment; filename=${applicationId}.pdf
Content-Transfer-Encoding: base64

${applicationPdfBase64}
${additionalAttachmentsData
  .map(
    (attachment) => `
--NextPart
Content-Type: application/pdf; name=${attachment.documentId}.pdf
Content-Disposition: attachment; filename=${attachment.documentId}.pdf
Content-Transfer-Encoding: base64

${attachment.base64Data}`
  )
  .join("")}
--NextPart--`;

    const params = {
      RawMessage: {
        Data: ses_mail,
      },
    };

    try {
      console.log("Sending email to ", emailAddress);
      console.log("Params: ", params);
      console.log("SES_SENDER_EMAIL: ", senderEmail);
      console.log("SES_REGION: ", process.env.REGION);
      console.log("SES_BUCKET_NAME: ", bucketName);
      console.log("SES_APPLICATION_PDF_BASE64: ", applicationPdfBase64);
      console.log("SES_ADDITIONAL_ATTACHMENTS_DATA: ", additionalAttachmentsData);
      console.log("SES_HTML_EMAIL_TEMPLATE: ", htmlEmailTemplate);
      console.log("SES_MAIL: ", ses_mail);
      const ses = new AWS.SES({ apiVersion: "2010-12-01" });
      await ses.sendRawEmail(params).promise();
    } catch (err) {
      console.log("[SERVER ERROR] Error in send email: " + err);
      return {
        message: "Error sending email",
      };
    }
  }

  return { message: "Email sent successfully" };
};

export const generateCobrandingLink = async (email: string, brand: string) => {
  try {
    const response = await axios.get(
      `${process.env.APPHERO_SERVER_DOMAIN}/unauth/apphero/cobrandedurl?email=${email}&brand=${brand}`
    );
    return response.data.appHeroUrl;
  } catch (error) {
    console.error("Error generating cobranding link:", error);
    return process.env.APPHERO_DOMAIN;
  }
};
