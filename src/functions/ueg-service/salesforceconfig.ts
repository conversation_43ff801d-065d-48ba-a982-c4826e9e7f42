export const salesforceAgentSubObjectsConfig = {
  documents: {
    applicationId: "ApplicationId__c",
    documentType: "DocumentType__c",
    documentName: ["Name", "OriginalValue__c", "FilePath__c"],
    opportunityId: "Opportunity__c",
    path: ["S3FileName__c", "FullUrl__c"],
    bucketName: "BucketName__c",
    institutionName: "Additional_Info__c",
    eduReference: "Related_Education_History__c",
    testReference: "Related_Language_Proficiency__c",
    identityReference: "Related_Proof_of_Identity__c",
    isIdentityInformationAccurate: "Is_Info_Accurate__c",
  },
  educationHistory: {
    institutionType: "Institution_Type__c",
    //EQHE
    dateOfEQHE: "GraduationDate__c",
    cityOfEQHE: "City__c",
    countryOfEQHEDisplayName: "Country__c",
    federalStates: "Province_State__c",
    originalTitleOfEQHE: "AwardDegree__c",

    //U1
    countryOfInitialRegistrationDisplayName: "Country__c",
    universityOfInitialRegistration: "InstitutionName__c",
    otherUniversityName: "InstitutionName__c",
    semesterOfInitialRegistration: "EnrolmentDateYear__c",

    institutionName: "InstitutionName__c",
    universityName: "InstitutionName__c",
    // degree: "Degree_earned__c",
    specialisation: "Specialisation__c",
    firstEnrolmentDate: "EnrolmentDateYear__c",
    lastEnrolmentDate: "GraduationDate__c",
    educationCity: "City__c",
    universityCountryDisplayName: "Country__c",
    programmeCompleted: "AwardDegree__c",
    institutionOrder: "Name",
    otherPartnerUniversityName: "Institute_Name__c",
  },
  languageProficiency: {
    proficiencyQualification: "ProficiencyQualification__c",
    testDate: "TestDate__c",
    overallScore: "TestScore__c",
    provider: "TestProvider__c",
    dateOfGraduation: "TestDate__c",
    otherTestName: "TestProvider__c",
    testVerificationLink: "Test_link_Certification__c",
    listeningScore: "Listening_Score__c",
    speakingScore: "Speaking_Score__c",
    readingScore: "Reading_Score__c",
    writingScore: "Writing_Score__c",
  },
  workHistory: {
    nameOfOrganization: "Employer__c",
    startDate: "StartDate__c",
    endDate: "EndDate__c",
    positionTitle: "Position__c",
    addressOfOrganization: "EmployerAddress__c",
    positionStatus: "Position_Type__c",
    positionLevel: "NatureOfDuties__c",
  },
  teamMembers: {
    opportunityAccessLevel: "OpportunityAccessLevel",
    teamMemberRole: "TeamMemberRole",
    userId: "UserId",
  },
  identityDocumentObject: {
    identityNumber: "Identity_Document_Number__c",
    identityIssueDate: "Issue_Date__c",
    identityExpiryDate: "Expiry_Date__c",
    identityType: "Identity_Type__c",
    identityIssuingCountry: "Issuing_Country__c",
  },
};

export const salesforceAgentConfig = {
  IdentityInfoRecord__c: ["identityDocumentObject"],
  LanguageProficiencyRecord__c: ["languageProficiency"],
  EducationHistoryRecord__c: ["educationHistory"],
  OpportunityFile__c: ["documents"],
  Connection__c: ["connections"],
  WorkHistoryRecord__c: ["workHistory"],
  Lead: {
    firstName: "FirstName",
    lastName: "LastName",
    email: "Email",
    "phoneNumber.numberWithCode": "Phone",
    countryDisplayName: ["Country__c", "Country"],
    program: "Programme__c",
    mailingState: "State__c",
    leadSource: "LeadSource",
    brand: "Brand__c",
    leadRecordTypeId: "RecordTypeId",
    businessUnit: "BusinessUnit__c",
    businessUnitFilter: "BusinessUnitFilter__c",
    agentAccountId: "AgentAccount__c",
    agentContactId: "Agent_Contact__c",
    accountManagerUserId: "BusinessDeveloper__c",
    agentContactUserId: "OwnerId",
    privacyPolicyConsent: [
      "HasOptedOutOfMarketingEmailSync__c",
      "HasOptedOutOfGUSMarketingEmail__c",
    ],
  },
  Account: {
    firstName: "FirstName",
    lastName: "LastName",
    email: "PersonEmail",
    middleName: "Middle_Name__c",
    "phoneNumber.numberWithCode": "Mobile__c",
    correspondenceLanguage: "CommunicationLanguage__c",
    countryDisplayName: [
      "Country__c",
      "PersonMailingCountry",
    ],
    countryOfBirthDisplayName: "CountryOfBirth__c",
    birthDate: "DateOfBirth__c",
    placeOfBirth: "PlaceOfBirth__c",
    citizenshipDisplayName: "Citizenship__c",
    "otherNumber.numberWithCode": "PersonOtherPhone",
    gender: "Gender__c",
    streetAddress: "PersonMailingStreet",
    city: ["gaconnector_City__c", "PersonMailingCity"],
    postalCode: "PersonMailingPostalCode",
    passportNumber: "Passport__pc",
    country: "PersonMailingCountryCode",
    correspondenceStreetAddress: "BillingStreet",
    correspondenceCity: "BillingCity",
    correspondencePostalCode: "BillingPostalCode",
    correspondenceCountry: "BillingCountryCode",
    correspondenceCountryDisplayName: "BillingCountry",
    businessUnit: "BusinessUnit__c",
    accountRecordTypeId: "RecordTypeId",
    brand: "Brand__c",
    programTypeDisplayName: "Level__pc",
    agentContactUserId: "OwnerId",
    privacyPolicyConsent: [
      "HasOptedOutOfMarketingEmail__c",
      "HasOptedOutOfGUSMarketingEmail__c",
    ],
  },
  Opportunity: {
    miscDetails: "Application_Misc_Details__c",
    // programType: "level__c",
    program: "Programme__c",
    partnerInstitution: "PathwayProviderId__c",
    "phoneNumber.numberWithCode": "AccountPhone__c",
    location: "Location__c",
    startTerm: ["CloseDate", "Product_Intake_Date__c"],
    citizenshipDisplayName: "Citizenship__c",
    city: "gaconnector_City__c",
    mailingState: "State__c",
    declaration3: "DeclarationInfoProvided__c",
    appId: "ApplicationId__c",
    agentContactId: "Agent_Contact__c",
    agentAccountId: "AgentAccount__c",
    accountManagerUserId: "BusinessDeveloper__c",
    agentContactUserId: "OwnerId",
    opportunityRecordTypeId: "RecordTypeId",
    businessUnit: "BusinessUnit__c",
    applicationId: "ApplicationFormId__c",
    programDisplayName: "Name",
    stage: "StageName",
    opportunityApplicationSource: "ApplicationSource__c",
    progress: "ApplicationProgress__c",
    isSubmitted: "ApplicationSubmitted__c",
    pricebookId: "Pricebook2Id",
    admissionStage: "AdmissionsStage__c",
  },
  Application__c: {
    programType: "Level_Of_Study__c",
    program: "Programme__c",
    startTerm: "Intake__c",
    firstName: "First_Name__c",
    birthDate: "Date_of_birth__c",
    middleName: "Middle_Other_Name_s__c",
    email: "Email__c",
    streetAddress: "Street_Address__c",
    city: "City__c",
    citizenshipDisplayName: "Citizenship__c",
    postalCode: "PostCode__c",
    accountManagerUserId: "Business_Developer__c",
    agentContactUserId: "OwnerId",
    applicationId: "Application_Form_Id__c",
    applicationRecordTypeId: "RecordTypeId",
    agentAccountId: "Agent_Account__c",
    agentContactId: "Agent_Contact__c",
    programDisplayName: "Program_Of_Study__c",
    "phoneNumber.numberWithCode": "Mobile__c",
    fullName: "Name",
  },
  OpportunityLineItem: {
    productId: "Product2Id",
  },
  OpportunityTeamMember: ["teamMembers"],
};

export const salesforceStudentSubObjectsConfig = {
  documents: {
    applicationId: "ApplicationId__c",
    documentType: "DocumentType__c",
    documentName: ["Name", "OriginalValue__c", "FilePath__c"],
    opportunityId: "Opportunity__c",
    path: ["S3FileName__c", "FullUrl__c"],
    bucketName: "BucketName__c",
    institutionName: "Additional_Info__c",
    eduReference: "Related_Education_History__c",
    testReference: "Related_Language_Proficiency__c",
    identityReference: "Related_Proof_of_Identity__c",
    isIdentityInformationAccurate: "Is_Info_Accurate__c",
  },
  educationHistory: {
    institutionType: "Institution_Type__c",
    //EQHE
    dateOfEQHE: "GraduationDate__c",
    cityOfEQHE: "City__c",
    countryOfEQHEDisplayName: "Country__c",
    federalStates: "Province_State__c",
    originalTitleOfEQHE: "AwardDegree__c",

    //U1
    countryOfInitialRegistrationDisplayName: "Country__c",
    universityOfInitialRegistration: "InstitutionName__c",
    otherUniversityName: "InstitutionName__c",
    semesterOfInitialRegistration: "EnrolmentDateYear__c",

    institutionName: "InstitutionName__c",
    universityName: "InstitutionName__c",
    // degree: "Degree_earned__c",
    specialisation: "Specialisation__c",
    firstEnrolmentDate: "EnrolmentDateYear__c",
    lastEnrolmentDate: "GraduationDate__c",
    educationCity: "City__c",
    universityCountryDisplayName: "Country__c",
    programmeCompleted: "AwardDegree__c",
    institutionOrder: "Name",
    otherPartnerUniversityName: "Institute_Name__c",
  },
  languageProficiency: {
    proficiencyQualification: "ProficiencyQualification__c",
    testDate: "TestDate__c",
    overallScore: "TestScore__c",
    provider: "TestProvider__c",
    dateOfGraduation: "TestDate__c",
    otherTestName: "TestProvider__c",
    testVerificationLink: "Test_link_Certification__c",
    listeningScore: "Listening_Score__c",
    speakingScore: "Speaking_Score__c",
    readingScore: "Reading_Score__c",
    writingScore: "Writing_Score__c",
  },
  workHistory: {
    nameOfOrganization: "Employer__c",
    startDate: "StartDate__c",
    endDate: "EndDate__c",
    positionTitle: "Position__c",
    addressOfOrganization: "EmployerAddress__c",
    positionStatus: "Position_Type__c",
    positionLevel: "NatureOfDuties__c",
  },
  teamMembers: {
    opportunityAccessLevel: "OpportunityAccessLevel",
    teamMemberRole: "TeamMemberRole",
    userId: "UserId",
  },
  identityDocumentObject: {
    identityNumber: "Identity_Document_Number__c",
    identityIssueDate: "Issue_Date__c",
    identityExpiryDate: "Expiry_Date__c",
    identityType: "Identity_Type__c",
    identityIssuingCountry: "Issuing_Country__c",
  },
  visaApplication: {
    visaRequired: "Visa_Required__c",
  },
};

export const salesforceStudentConfig = {
  IdentityInfoRecord__c: ["identityDocumentObject"],
  LanguageProficiencyRecord__c: ["languageProficiency"],
  EducationHistoryRecord__c: ["educationHistory"],
  OpportunityFile__c: ["documents"],
  WorkHistoryRecord__c: ["workHistory"],
  Connection__c: ["connections"],
  Visa_Application__c: ["visaApplication"],
  Lead: {
    firstName: "FirstName",
    lastName: "LastName",
    email: "Email",
    "phoneNumber.numberWithCode": "Phone",
    countryDisplayName: ["Country__c", "Country"],
    program: "Programme__c",
    mailingState: "State__c",
    leadSource: "LeadSource",
    brand: "Brand__c",
    "utmParams.utmSource": "pi__utm_source__c",
    "utmParams.utmMedium": "pi__utm_medium__c",
    "utmParams.utmCampaign": "pi__utm_campaign__c",
    "utmParams.utmContent": "pi__utm_content__c",
    "utmParams.utmTerm": "pi__utm_term__c",
    "utmParams.utmNetwork": "utm_network__c",
    "utmParams.utmReferrer": "Utm_Referrer__c",
    sourceWebsite: "Source_Website__c",
    opportunityApplicationSource: ["ApplicationSource__c", "Vendor__c"],
    leadRecordTypeId: "RecordTypeId",
    businessUnit: "BusinessUnit__c",
    businessUnitFilter: "BusinessUnitFilter__c",
    privacyPolicyConsent: [
      "HasOptedOutOfMarketingEmailSync__c",
      "HasOptedOutOfGUSMarketingEmail__c",
    ],
  },
  Account: {
    firstName: "FirstName",
    lastName: "LastName",
    email: "PersonEmail",
    "phoneNumber.numberWithCode": "Mobile__c",
    correspondenceLanguage: "CommunicationLanguage__c",
    countryDisplayName: [
      "Country__c",
      "PersonMailingCountry",
    ],
    countryOfBirthDisplayName: "CountryOfBirth__c",
    middleName: "Middle_Name__c",
    birthDate: "DateOfBirth__c",
    placeOfBirth: "PlaceOfBirth__c",
    citizenshipDisplayName: "Citizenship__c",
    "otherNumber.numberWithCode": "PersonOtherPhone",
    gender: "Gender__c",
    streetAddress: "PersonMailingStreet",
    city: ["gaconnector_City__c", "PersonMailingCity"],
    postalCode: "PersonMailingPostalCode",
    passportNumber: "Passport__pc",
    country: "PersonMailingCountryCode",
    correspondenceStreetAddress: "BillingStreet",
    correspondenceCity: "BillingCity",
    correspondencePostalCode: "BillingPostalCode",
    correspondenceCountry: "BillingCountryCode",
    correspondenceCountryDisplayName: "BillingCountry",
    businessUnit: "BusinessUnit__c",
    accountRecordTypeId: "RecordTypeId",
    brand: "Brand__c",
    programTypeDisplayName: "Level__pc",
    privacyPolicyConsent: [
      "HasOptedOutOfMarketingEmail__c",
      "HasOptedOutOfGUSMarketingEmail__c",
    ],
  },
  Opportunity: {
    miscDetails: "Application_Misc_Details__c",
    // programType: "level__c",
    program: "Programme__c",
    partnerInstitution: "PathwayProviderId__c",
    "phoneNumber.numberWithCode": "AccountPhone__c",
    location: "Location__c",
    startTerm: ["CloseDate", "Product_Intake_Date__c"],
    citizenshipDisplayName: "Citizenship__c",
    city: "gaconnector_City__c",
    mailingState: "State__c",
    declaration3: "DeclarationInfoProvided__c",
    appId: "ApplicationId__c",
    opportunityRecordTypeId: "RecordTypeId",
    businessUnit: "BusinessUnit__c",
    applicationId: "ApplicationFormId__c",
    programDisplayName: "Name",
    stage: "StageName",
    opportunityApplicationSource: "ApplicationSource__c",
    progress: "ApplicationProgress__c",
    isSubmitted: "ApplicationSubmitted__c",
    pricebookId: "Pricebook2Id",
    admissionStage: "AdmissionsStage__c",
  },
  Application__c: {
    programType: "Level_Of_Study__c",
    program: "Programme__c",
    startTerm: "Intake__c",
    firstName: "First_Name__c",
    middleName: "Middle_Other_Name_s__c",
    email: "Email__c",
    birthDate: "Date_of_birth__c",
    streetAddress: "Street_Address__c",
    city: "City__c",
    citizenshipDisplayName: "Citizenship__c",
    postalCode: "PostCode__c",
    accountManagerUserId: "Business_Developer__c",
    applicationId: "Application_Form_Id__c",
    applicationRecordTypeId: "RecordTypeId",
    programDisplayName: "Program_Of_Study__c",
    "phoneNumber.numberWithCode": "Mobile__c",
    fullName: "Name",
  },
  OpportunityLineItem: {
    productId: "Product2Id",
  },
  OpportunityTeamMember: ["teamMembers"],
};
